<template>
  <div class="container interface-config-page">
    <div class="page-header">
      <div class="page-title">
        <el-icon><Setting /></el-icon>
        <span class="title-text">接口配置管理</span>
      </div>
      <div class="header-actions">
        <!-- 视图切换按钮 -->
        <div class="view-toggle">
          <el-button-group>
            <el-button
              :type="currentView === 'card' ? 'primary' : ''"
              @click="currentView = 'card'"
            >
              <el-icon><Grid /></el-icon>
            </el-button>
            <el-button
              :type="currentView === 'table' ? 'primary' : ''"
              @click="currentView = 'table'"
            >
              <el-icon><List /></el-icon>
            </el-button>
          </el-button-group>
        </div>

        <SearchComponent
          v-model="searchQuery"
          placeholder="搜索接口名称或路径"
          width="280px"
          @search="handleSearch"
          @clear="handleSearch"
        />
        <el-select v-model="filterGroup" placeholder="选择分组" clearable @change="handleSearch" style="width: 160px;">
          <el-option label="全部" :value="''" />
          <el-option
            v-for="group in interfaceGroups"
            :key="group.id"
            :label="group.name"
            :value="group.id"
          />
        </el-select>
        <el-select v-model="filterMethod" placeholder="HTTP方法" clearable @change="handleSearch" style="width: 120px;">
          <el-option label="全部" :value="''" />
          <el-option
            v-for="method in httpMethods"
            :key="method.value"
            :label="method.label"
            :value="method.value"
          />
        </el-select>
        <el-button type="primary" @click="handleAdd">新增接口</el-button>
        <el-button
          type="success"
          @click="handleBatchTest"
          :loading="batchTesting"
          :disabled="interfaceConfigs.length === 0"
        >
          <el-icon><VideoPlay /></el-icon>
          批量测试
        </el-button>
        <el-button @click="loadInterfaceConfigs">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

        <!-- 批量测试进度条 -->
        <div v-if="batchTesting" class="batch-test-progress">
          <div class="progress-header">
            <span class="progress-title">
              <el-icon class="rotating"><Loading /></el-icon>
              正在批量测试接口...
            </span>
            <el-button size="small" type="danger" plain @click="cancelBatchTest">
              <el-icon><Close /></el-icon>
              取消测试
            </el-button>
          </div>

          <el-progress
            :percentage="testProgress"
            :status="testProgressStatus"
            :stroke-width="8"
            :show-text="true"
          >
            <template #default="{ percentage }">
              <span class="progress-text">
                {{ completedTests }}/{{ totalTests }} ({{ percentage }}%)
              </span>
            </template>
          </el-progress>

          <div class="progress-details">
            <span class="current-batch">{{ currentBatchInfo }}</span>
            <span class="success-count">成功: {{ successCount }}</span>
            <span class="fail-count">失败: {{ failCount }}</span>
          </div>
        </div>

        <!-- 卡片视图 -->
        <div v-if="currentView === 'card'" class="card-container">
          <div v-loading="loading" class="base-card-grid">
            <!-- 空状态 -->
            <div v-if="interfaceConfigs.length === 0 && !loading" class="empty-state">
              <el-empty
                description="暂无接口配置数据"
                :image-size="120"
              >
                <template #description>
                  <p>还没有创建任何接口配置</p>
                  <p>点击上方"新增接口"按钮开始创建</p>
                </template>
                <el-button type="primary" @click="handleAdd">
                  <el-icon><Plus /></el-icon>
                  新增接口
                </el-button>
              </el-empty>
            </div>

            <!-- 接口配置卡片 -->
            <BaseCard
              v-for="config in interfaceConfigs"
              :key="config.id"
              :item="config"
              :config="{
                titleField: 'name',
                statusField: 'isEnabled',
                size: 'small',
                shadow: 'hover',
                style: 'compact-info'
              }"
              :show-default-header="false"
              :show-default-actions="false"
              @click="handleView(config)"
              @edit="(item, event) => handleEdit(item, event)"
              @test="(item, event) => handleTest(item, event)"
              @delete="(item, event) => handleDelete(item, event)"
              style="cursor: pointer"
            >
              <template #content="{ item }">
                <div class="interface-card-content">
                  <!-- 卡片头部：接口名称 + 状态 -->
                  <div class="card-header">
                    <h4 class="card-title" :title="item.name">{{ item.name }}</h4>
                    <el-tag :type="item.isEnabled ? 'success' : 'danger'" size="small" class="status-tag">
                      <el-icon class="status-icon">
                        <CircleCheck v-if="item.isEnabled" />
                        <CircleClose v-else />
                      </el-icon>
                      {{ item.isEnabled ? '启用' : '禁用' }}
                    </el-tag>
                  </div>

                  <!-- 接口信息：分行显示 -->
                  <div class="interface-info">
                    <!-- 第一行：路由信息 -->
                    <div class="info-row">
                      <div class="info-item">
                        <el-icon class="info-icon"><Link /></el-icon>
                        <el-tag :type="getMethodType(item.method)" size="small">{{ item.method }}</el-tag>
                        <span class="route-path" :title="item.path">{{ item.path }}</span>
                      </div>
                    </div>

                    <!-- 第二行：分组信息 -->
                    <div class="info-row">
                      <div class="info-item">
                        <el-icon class="info-icon"><FolderOpened /></el-icon>
                        <span class="info-label">组名</span>
                        <span class="info-text group-name" :title="item.groupName || '未分组'">{{ item.groupName || '未分组' }}</span>
                      </div>
                    </div>

                    <!-- 第三行：标签信息（始终显示，保持卡片高度一致） -->
                    <div class="info-row">
                      <div class="info-item">
                        <el-icon class="info-icon"><PriceTag /></el-icon>
                        <div v-if="item.tagNames && item.tagNames.length > 0" class="tag-list">
                          <el-tag
                            v-for="tagName in item.tagNames"
                            :key="tagName"
                            size="small"
                            type="info"
                          >
                            {{ tagName }}
                          </el-tag>
                        </div>
                        <span v-else class="no-tags">无标签</span>
                      </div>
                    </div>
                  </div>


                </div>
              </template>

              <template #actions="{ item }">
                <div class="action-bar-content" style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                  <!-- 左侧：测试状态 -->
                  <div class="status-left" style="display: flex; align-items: center;">
                    <el-tag :type="getTestStatusType(item.testStatus)" size="small" class="test-status-tag">
                      {{ getTestStatusText(item.testStatus) }}
                    </el-tag>
                  </div>

                  <!-- 右侧：操作按钮 -->
                  <div class="actions-right" style="display: flex; align-items: center; gap: 6px;">
                    <el-button type="primary" size="small" circle @click="handleEdit(item, $event)" title="编辑">
                      <el-icon><Edit /></el-icon>
                    </el-button>
                    <el-button size="small" type="info" circle @click="handleTest(item, $event)" title="测试">
                      <el-icon><VideoPlay /></el-icon>
                    </el-button>
                    <el-button size="small" type="danger" circle @click="handleDelete(item, $event)" title="删除">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                </div>
              </template>
            </BaseCard>
          </div>
        </div>

        <!-- 表格视图 -->
        <el-table
          v-if="currentView === 'table'"
          v-loading="loading"
          :data="interfaceConfigs"
          style="width: 100%;"
          :row-style="{ height: '60px' }"
          :cell-style="{ padding: '12px 0' }"
          :scroll-x="true"
        >
          <!-- 空状态 -->
          <template #empty>
            <el-empty
              description="暂无接口配置数据"
              :image-size="120"
            >
              <template #description>
                <p>还没有创建任何接口配置</p>
                <p>点击上方"新增接口"按钮开始创建</p>
              </template>
              <el-button type="primary" @click="handleAdd">
                <el-icon><Plus /></el-icon>
                新增接口
              </el-button>
            </el-empty>
          </template>
          <el-table-column label="接口名称" prop="name" width="150" align="left" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="interface-name">
                <span class="name-text">{{ row.name }}</span>
                <div class="interface-tags" v-if="row.tagNames && row.tagNames.length > 0">
                  <el-tag
                    v-for="tagName in row.tagNames"
                    :key="tagName"
                    size="small"
                    type="info"
                  >
                    {{ tagName }}
                  </el-tag>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="路由信息" width="250" align="left">
            <template #default="{ row }">
              <div class="route-info">
                <el-tag
                  :color="getMethodColor(row.method)"
                  size="small"
                  style="color: white; border: none; margin-right: 8px;"
                >
                  {{ row.method }}
                </el-tag>
                <code class="path-text">{{ row.path }}</code>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="分组/数据源" width="150" align="left">
            <template #default="{ row }">
              <div class="group-datasource-info">
                <div class="group-name">
                  <el-icon><Coin /></el-icon>
                  <span class="group-text">{{ row.groupName || '未分组' }}</span>
                </div>
                <div class="datasource-name">
                  <el-icon><Setting /></el-icon>
                  <span class="datasource-text">{{ row.datasourceName }}</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="状态/权限" width="100" align="center">
            <template #default="{ row }">
              <div class="status-permission-info">
                <div class="status-row">
                  <el-tag :type="row.isEnabled ? 'success' : 'danger'" size="small">
                    {{ row.isEnabled ? '启用' : '禁用' }}
                  </el-tag>
                </div>
                <div class="permission-row">
                  <el-tag :type="row.isPublic ? 'warning' : 'info'" size="small">
                    {{ row.isPublic ? '公开' : '私有' }}
                  </el-tag>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="测试状态" width="100" align="center">
            <template #default="{ row }">
              <div class="test-status">
                <el-tag
                  v-if="row.testStatus === 'success'"
                  type="success"
                  size="small"
                  class="clickable-tag"
                  @click="handleViewTestResult(row)"
                >
                  成功
                </el-tag>
                <el-tag
                  v-else-if="row.testStatus === 'failed'"
                  type="danger"
                  size="small"
                  class="clickable-tag"
                  @click="handleViewTestResult(row)"
                >
                  失败
                </el-tag>
                <el-tag
                  v-else-if="row.testStatus === 'pending'"
                  type="warning"
                  size="small"
                >
                  测试中
                </el-tag>
                <span v-else class="no-test">未测试</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="更新时间" prop="updatedAt" width="120" align="center">
            <template #default="{ row }">
              <span class="time-text">{{ row.updatedAt }}</span>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="180" fixed="right" align="center">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
                <el-button
                  size="small"
                  type="info"
                  @click="handleTest(row)"
                  :loading="testingItems.has(row.id)"
                  :disabled="testingItems.has(row.id)"
                >
                  {{ testingItems.has(row.id) ? '测试中' : '测试' }}
                </el-button>
                <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <PaginationComponent
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />

    <!-- 删除确认对话框 -->
    <ConfirmDialog
      v-model="deleteDialogVisible"
      title="删除接口"
      :content="`确定要删除接口 '${deleteItem?.name}' 吗？删除后相关的测试记录也将被清除。`"
      @confirm="confirmDelete"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Setting, Coin, Refresh, Plus, Grid, List, Link, FolderOpened, PriceTag, Clock, CircleCheck, CircleClose, Edit, VideoPlay, Delete } from '@element-plus/icons-vue';
import BaseCard from '@/components/common/BaseCard.vue';
import ConfirmDialog from '@/components/common/ConfirmDialog.vue';
import SearchComponent from '@/components/common/SearchComponent.vue';
import PaginationComponent from '@/components/common/PaginationComponent.vue';
import interfaceConfigService from '@/services/interface-config.service';
import interfaceGroupService from '@/services/interface-group.service';
import interfaceTagService from '@/services/interface-tag.service';
import dataSourceService from '@/services/datasource.service';
import type { InterfaceConfig, HttpMethodOption } from '@/types/interface-config';
import type { InterfaceGroup } from '@/types/interface-group';
import type { InterfaceTag } from '@/types/interface-tag';
import type { DataSource } from '@/types/datasource';
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger';
import { HTTP_METHODS } from '@/types/interface-config';

import { pageRefreshUtil, PAGE_KEYS } from '@/utils/pageRefreshUtil';
import { Loading, Close } from '@element-plus/icons-vue';

// 全局抽屉通信助手
const drawerMessenger = useGlobalDrawerMessenger();

// 视图状态管理
const currentView = ref<'card' | 'table'>('card'); // 默认卡片视图

// 监听视图模式切换，自动调整分页大小
watch(currentView, (newView) => {
  if (newView === 'card') {
    pageSize.value = 15; // 卡片视图：15条
  } else {
    pageSize.value = 10; // 表格视图：10条
  }
  currentPage.value = 1; // 重置到第一页
  loadInterfaceConfigs(); // 重新加载数据
});

// 列表数据
const loading = ref(false);
const interfaceConfigs = ref<InterfaceConfig[]>([]);
const searchQuery = ref('');
const filterGroup = ref<number | undefined>();
const filterMethod = ref<string | undefined>();
const currentPage = ref(1);
const pageSize = ref(15); // 卡片视图默认15条，表格视图10条
const totalCount = ref(0);

// 测试相关
const testingItems = ref<Set<number>>(new Set());

// 批量测试相关状态
const batchTesting = ref(false);
const testProgress = ref(0);
const testProgressStatus = ref<'success' | 'exception' | 'warning' | ''>('');
const completedTests = ref(0);
const totalTests = ref(0);
const successCount = ref(0);
const failCount = ref(0);
const currentBatchInfo = ref('');
const cancelToken = ref<AbortController | null>(null);

// 批量测试配置（改为串行执行，确保用户看到逐个完成的进度）

// 下拉选项数据
const interfaceGroups = ref<InterfaceGroup[]>([]);
const interfaceTags = ref<InterfaceTag[]>([]);
const dataSources = ref<DataSource[]>([]);
const httpMethods: HttpMethodOption[] = HTTP_METHODS;

// 删除确认相关
const deleteDialogVisible = ref(false);
const deleteItem = ref<InterfaceConfig | null>(null);

// 加载接口配置列表
const loadInterfaceConfigs = async () => {
  loading.value = true;
  try {
    const response = await interfaceConfigService.getInterfaceConfigs({
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchQuery.value || undefined,
      groupId: filterGroup.value || undefined,
      method: filterMethod.value || undefined
    });

    // 确保每个接口都有testStatus字段
    interfaceConfigs.value = response.items.map(item => ({
      ...item,
      testStatus: item.testStatus || null // 确保testStatus字段存在
    }));
    totalCount.value = response.total;

    // 测试状态已经在接口配置中，不需要单独加载
  } catch (error) {
    ElMessage.error('加载接口配置列表失败');
  } finally {
    loading.value = false;
  }
};

// 测试状态现在直接从接口配置中获取，不需要单独加载

// 刷新到第一页的方法
const loadInterfaceConfigsToFirstPage = async () => {
  currentPage.value = 1;
  await loadInterfaceConfigs();
};

// 获取HTTP方法颜色
const getMethodColor = (method: string): string => {
  const methodConfig = httpMethods.find(m => m.value === method);
  return methodConfig?.color || '#909399';
};

// 获取HTTP方法类型（用于卡片视图的标签颜色）
const getMethodType = (method: string): string => {
  const methodMap: Record<string, string> = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger',
    'PATCH': 'info'
  };
  return methodMap[method] || 'info';
};

// 格式化日期（用于卡片视图）
const formatDate = (dateString: string): string => {
  if (!dateString) return '未知';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
};

// 获取测试状态文本
const getTestStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'success': '测试成功',
    'failed': '测试失败',
    'pending': '测试中...'
  };
  return statusMap[status] || '未测试';
};

// 获取测试状态类型（用于标签颜色）
const getTestStatusType = (status: string): string => {
  const statusMap: Record<string, string> = {
    'success': 'success',
    'failed': 'danger',
    'pending': 'warning'
  };
  return statusMap[status] || 'info';
};

// 查看接口配置详情
const handleView = async (config: InterfaceConfig) => {
  try {
    // 从后端获取完整的接口配置信息
    const fullConfig = await interfaceConfigService.getInterfaceConfigById(config.id);
    if (!fullConfig) {
      ElMessage.error('接口配置不存在或已被删除');
      return;
    }

    drawerMessenger.showDrawer({
      title: '查看接口配置',
      component: 'InterfaceConfigForm',
      props: {
        isEdit: false,
        isView: true,  // 标识为查看模式
        editData: fullConfig
      },
      size: '40%'
    });
  } catch (error) {
    console.error('获取接口配置详情失败:', error);
    ElMessage.error('获取接口配置详情失败');
  }
};

// 测试单个接口
const handleTest = async (row: InterfaceConfig, event?: Event) => {
  if (event) {
    event.stopPropagation(); // 阻止事件冒泡
  }
  // 添加到测试中的接口列表
  testingItems.value.add(row.id);

  // 设置测试状态为"测试中"
  updateInterfaceTestStatusInList(row.id, 'pending');

  const testStartTime = Date.now(); // 记录测试开始时间

  try {
    ElMessage.info(`开始测试接口: ${row.name}`);

    // 第一步：从数据库获取接口配置详情
    const dbConfig = await interfaceConfigService.getInterfaceConfigById(row.id);

    if (!dbConfig) {
      throw new Error('无法获取接口配置详情');
    }

    // 第二步：处理路径，去掉开头的 /api 部分
    let cleanPath = dbConfig.path;
    if (cleanPath.startsWith('/api')) {
      cleanPath = cleanPath.substring(4); // 去掉 '/api' 部分
    }

    // 第三步：使用处理后的路径构造测试URL（动态获取域名）
    const currentHost = window.location.hostname;
    const dynamicApiUrl = `http://${currentHost}:8000/api/dynamic${cleanPath}`;
    const testUrl = `${dynamicApiUrl}?page=1&size=5`;

    // 发送简单的GET请求，获取少量数据进行连通性测试
    const response = await fetch(testUrl, {
      method: 'GET', // 基础测试只用GET方法
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const data = await response.json();

      // 更严格的成功判断：检查是否有实际数据
      let isSuccess = false;
      let successMessage = '';
      let dataCount = 0;

      if (data && typeof data === 'object') {
        // 检查后端返回的错误标识
        if (data.success === false) {
          isSuccess = false;
          successMessage = `接口测试失败: ${row.name} (后端返回错误: ${data.message || '未知错误'})`;
        }
        // 检查是否有数据内容
        else if (data.data && Array.isArray(data.data)) {
          // 标准的data数组格式
          dataCount = data.data.length;
          isSuccess = true;
          successMessage = dataCount > 0
            ? `接口测试成功: ${row.name} (返回 ${dataCount} 条数据)`
            : `接口测试成功: ${row.name} (返回空数据，接口正常)`;
        } else if (data.items && Array.isArray(data.items)) {
          // 分页格式的数据
          dataCount = data.items.length;
          isSuccess = true;
          successMessage = `接口测试成功: ${row.name} (分页数据 ${dataCount} 条，总数: ${data.total || 'N/A'})`;
        } else if (data.records && Array.isArray(data.records)) {
          // 其他可能的数组格式
          dataCount = data.records.length;
          isSuccess = true;
          successMessage = `接口测试成功: ${row.name} (返回 ${dataCount} 条记录)`;
        } else if (data.list && Array.isArray(data.list)) {
          // 列表格式
          dataCount = data.list.length;
          isSuccess = true;
          successMessage = `接口测试成功: ${row.name} (返回 ${dataCount} 条列表数据)`;
        } else if (Object.keys(data).length > 0 && !data.error) {
          // 有其他格式的数据内容，且没有错误标识
          isSuccess = true;
          successMessage = `接口测试成功: ${row.name} (返回数据对象)`;
        } else {
          // 即使数据结构不符合预期，只要有响应就算成功（基础连通性测试）
          isSuccess = true;
          successMessage = `接口测试成功: ${row.name} (接口连通正常)`;
        }
      } else {
        // 响应不是有效的JSON对象
        isSuccess = false;
        successMessage = `接口测试失败: ${row.name} (响应格式无效)`;
      }

      // 构造测试记录数据
      const testRecord = {
        interface_id: row.id,
        interface_name: dbConfig.name,
        test_url: testUrl,
        status_code: response.status,
        response_time: Date.now() - testStartTime,
        test_time: new Date().toISOString(),
        response_data: data,
        data_count: dataCount,
        success: isSuccess,
        error_message: isSuccess ? null : '响应成功但无有效数据结构',
        test_params: { page: 1, size: 5 },
        test_headers: { 'Content-Type': 'application/json' },
        db_config: dbConfig // 保存数据库配置用于调试
      };

      // 显示测试结果，但不自动保存到数据库
      if (isSuccess) {
        ElMessage.success(successMessage);
      } else {
        ElMessage.error(successMessage);
      }

      // 显示测试结果抽屉，让用户决定是否保存
      showTestResultDrawer(row, testRecord, isSuccess);

    } else {
      // HTTP状态码不是2xx
      const errorMessage = `接口测试失败: ${row.name} (状态码: ${response.status})`;
      ElMessage.error(errorMessage);

      const testRecord = {
        interface_id: row.id,
        interface_name: row.name,
        test_url: `${dynamicApiUrl}?page=1&size=5`,
        status_code: response.status,
        response_time: Date.now() - testStartTime,
        test_time: new Date().toISOString(),
        response_data: null,
        data_count: 0,
        success: false,
        error_message: `HTTP ${response.status}`,
        test_params: { page: 1, size: 5 },
        test_headers: { 'Content-Type': 'application/json' }
      };

      // 显示测试结果抽屉，让用户决定是否保存
      showTestResultDrawer(row, testRecord, false);
    }

  } catch (error: any) {
    const errorMessage = `接口测试异常: ${row.name} - ${error.message}`;
    ElMessage.error(errorMessage);

    // 构造失败的测试记录（使用前端显示的路径作为备用）
    const currentHost = window.location.hostname;
    const fallbackUrl = `http://${currentHost}:8000/api/dynamic${row.path}?page=1&size=5`;

    const testRecord = {
      interface_id: row.id,
      interface_name: row.name,
      test_url: fallbackUrl,
      status_code: 0,
      response_time: Date.now() - testStartTime,
      test_time: new Date().toISOString(),
      response_data: null,
      data_count: 0,
      success: false,
      error_message: error.message,
      test_params: { page: 1, size: 5 },
      test_headers: { 'Content-Type': 'application/json' }
    };

    // 显示测试结果抽屉，让用户决定是否保存
    showTestResultDrawer(row, testRecord, false);

  } finally {
    // 从测试中的接口列表移除
    testingItems.value.delete(row.id);

    // 刷新接口列表以显示最新的测试状态
    loadInterfaceConfigs();
  }
};

// 更新列表中的接口测试状态（仅用于测试中状态）
const updateInterfaceTestStatusInList = (interfaceId: number, status: 'pending' | 'success' | 'failed') => {
  // 只用于设置"测试中"状态，成功/失败状态通过数据库刷新获取
  const newConfigs = interfaceConfigs.value.map(config => {
    if (config.id === interfaceId) {
      return { ...config, testStatus: status };
    }
    return config;
  });

  interfaceConfigs.value = newConfigs;
};

// 更新接口测试状态 - 直接强制保存
const updateInterfaceTestStatus = async (interfaceId: number, status: 'success' | 'failed') => {
  try {
    // 构造最简单的测试状态数据（使用后端期望的snake_case格式）
    const testData = {
      test_status: status,
      last_test_at: new Date().toISOString()
    };
    // 直接使用fetch调用后端API，绕过前端service的复杂逻辑
    const url = `http://${window.location.hostname}:8000/api/v1/interface/configs/${interfaceId}`;
    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testData)
    });
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`强制保存失败: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    return true;

  } catch (error) {
    throw error;
  }
};

// 确认删除
const confirmDelete = async () => {
  if (!deleteItem.value) return;

  try {
    await interfaceConfigService.deleteInterfaceConfig(deleteItem.value.id);
    ElMessage.success('删除成功');

    // 刷新列表到第一页
    loadInterfaceConfigsToFirstPage();
  } catch (error) {
    ElMessage.error('删除失败');
  } finally {
    deleteDialogVisible.value = false;
    deleteItem.value = null;
  }
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  loadInterfaceConfigs();
};

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  loadInterfaceConfigs();
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  loadInterfaceConfigs();
};

// 新增接口
const handleAdd = () => {
  drawerMessenger.showDrawer({
    title: '新增接口',
    component: 'InterfaceConfigForm',
    props: {
      isEdit: false,
      editData: null
    },
    size: '40%'
  });
};

// 编辑接口
const handleEdit = async (row: InterfaceConfig, event?: Event) => {
  if (event) {
    event.stopPropagation(); // 阻止事件冒泡
  }

  try {
    // 通过API获取完整的接口配置数据
    const fullData = await interfaceConfigService.getInterfaceConfigById(row.id);

    if (!fullData) {
      ElMessage.error('获取接口配置详情失败');
      return;
    }

    drawerMessenger.showDrawer({
      title: '编辑接口',
      component: 'InterfaceConfigForm',
      props: {
        isEdit: true,
        editData: fullData  // 使用从后端获取的完整数据
      },
      size: '40%'
    });

  } catch (error) {
    ElMessage.error('获取接口配置详情失败');
  }
};

// 批量测试主函数
const handleBatchTest = async () => {
  if (interfaceConfigs.value.length === 0) {
    ElMessage.warning('当前页面没有接口可以测试');
    return;
  }

  // 确认对话框
  try {
    await ElMessageBox.confirm(
      `确定要测试当前页面的 ${interfaceConfigs.value.length} 个接口吗？`,
      '批量测试确认',
      {
        type: 'info',
        confirmButtonText: '开始测试',
        cancelButtonText: '取消'
      }
    );
  } catch {
    return; // 用户取消
  }

  // 初始化测试状态
  initBatchTestState();

  // 立即显示开始通知，让用户知道测试已开始
  ElMessage.info({
    message: `开始批量测试 ${interfaceConfigs.value.length} 个接口...`,
    duration: 2000
  });

  try {
    const results = await executeBatchTestWithConcurrency();
    await handleBatchTestComplete(results);
  } catch (error: any) {
    if (error.name !== 'AbortError') {
      ElMessage.error('批量测试过程中发生错误');
    }
  } finally {
    batchTesting.value = false;
  }
};

// 初始化批量测试状态
const initBatchTestState = () => {
  batchTesting.value = true;
  testProgress.value = 0;
  testProgressStatus.value = '';
  completedTests.value = 0;
  totalTests.value = interfaceConfigs.value.length;
  successCount.value = 0;
  failCount.value = 0;
  currentBatchInfo.value = '';
  cancelToken.value = new AbortController();
};

// 执行批量测试（串行执行，每完成一个立即显示进度）
const executeBatchTestWithConcurrency = async () => {
  const interfaces = interfaceConfigs.value;
  const results: any[] = [];

  // 串行执行每个接口，让用户看到逐个完成的效果
  for (let i = 0; i < interfaces.length; i++) {
    const config = interfaces[i];

    // 检查是否被取消
    if (cancelToken.value?.signal.aborted) {
      throw new Error('测试已取消');
    }

    // 更新当前测试信息
    currentBatchInfo.value = `正在测试: ${config.name} (${i + 1}/${interfaces.length})`;
    updateProgress();

    try {
      // 测试单个接口
      const testResult = await handleTestForBatch(config);

      // 立即更新计数和进度
      if (testResult.success) {
        successCount.value++;
      } else {
        failCount.value++;
      }

      // 显示完成信息
      currentBatchInfo.value = `已完成: ${config.name} (${i + 1}/${interfaces.length})`;
      updateProgress();

      results.push({
        id: config.id,
        name: config.name,
        success: testResult.success,
        result: testResult
      });

    } catch (error: any) {
      // 处理意外的异常
      failCount.value++;

      currentBatchInfo.value = `测试失败: ${config.name} (${i + 1}/${interfaces.length})`;
      updateProgress();

      results.push({
        id: config.id,
        name: config.name,
        success: false,
        error: error.message || '测试异常'
      });
    }

    // 短暂延迟，让用户看到进度更新
    await new Promise(resolve => setTimeout(resolve, 200));
  }

  return results;
};

// 更新进度
const updateProgress = () => {
  completedTests.value = successCount.value + failCount.value;
  testProgress.value = Math.round((completedTests.value / totalTests.value) * 100);

  // 更新进度条状态
  if (failCount.value > 0 && successCount.value > 0) {
    testProgressStatus.value = 'warning';
  } else if (failCount.value > 0) {
    testProgressStatus.value = 'exception';
  } else if (completedTests.value === totalTests.value) {
    testProgressStatus.value = 'success';
  }
};

// 处理批量测试完成
const handleBatchTestComplete = async (results: any[]) => {
  // 显示结果通知
  showBatchTestResult();

  // 刷新到第一页
  currentPage.value = 1;
  await loadInterfaceConfigs();
};

// 显示测试结果
const showBatchTestResult = () => {
  const message = `批量测试完成！成功: ${successCount.value}个，失败: ${failCount.value}个`;

  if (failCount.value === 0) {
    ElMessage.success({
      message,
      duration: 5000,
      showClose: true
    });
  } else if (successCount.value === 0) {
    ElMessage.error({
      message,
      duration: 5000,
      showClose: true
    });
  } else {
    ElMessage.warning({
      message,
      duration: 5000,
      showClose: true
    });
  }
};

// 取消批量测试
const cancelBatchTest = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消正在进行的批量测试吗？',
      '取消确认',
      { type: 'warning' }
    );

    cancelToken.value?.abort();
    batchTesting.value = false;

    ElMessage.info('批量测试已取消');
  } catch {
    // 用户取消了取消操作，继续测试
  }
};

// 批量测试专用的测试函数（复用原有逻辑但自动保存）
const handleTestForBatch = async (config: InterfaceConfig) => {
  testingItems.value.add(config.id);

  const testStartTime = Date.now();

  // 立即更新进度显示，让用户知道测试已开始
  updateProgress();

  try {
    // 第一步：从数据库获取接口配置详情
    const dbConfig = await interfaceConfigService.getInterfaceConfigById(config.id);

    if (!dbConfig) {
      throw new Error('无法获取接口配置详情');
    }

    // 第二步：处理路径，确保正确的动态API路径
    let cleanPath = dbConfig.path;
    // 如果路径以 /api 开头，去掉它
    if (cleanPath.startsWith('/api')) {
      cleanPath = cleanPath.substring(4); // 去掉 '/api'，保留 '/v1/project/clht'
    }
    // 确保路径以 / 开头
    if (!cleanPath.startsWith('/')) {
      cleanPath = '/' + cleanPath;
    }

    // 第三步：构造测试URL - 正确的格式应该是 /api/dynamic + cleanPath
    const currentHost = window.location.hostname;
    const testUrl = `http://${currentHost}:8000/api/dynamic${cleanPath}?page=1&size=5`;

    // 发送测试请求（添加超时控制）
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10秒超时

    try {
      const response = await fetch(testUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.ok) {
      const data = await response.json();

      // 判断测试是否成功（复用原有逻辑）
      let isSuccess = false;
      let dataCount = 0;

      if (data && typeof data === 'object') {
        if (data.success === false) {
          isSuccess = false;
        } else if (data.data && Array.isArray(data.data)) {
          dataCount = data.data.length;
          isSuccess = true;
        } else if (data.items && Array.isArray(data.items)) {
          dataCount = data.items.length;
          isSuccess = true;
        } else if (data.records && Array.isArray(data.records)) {
          dataCount = data.records.length;
          isSuccess = true;
        } else if (data.list && Array.isArray(data.list)) {
          dataCount = data.list.length;
          isSuccess = true;
        } else if (Object.keys(data).length > 0 && !data.error) {
          isSuccess = true;
        } else {
          isSuccess = true; // 基础连通性测试
        }
      }

      // 构造测试记录（与原有格式一致）
      const testRecord = {
        interface_id: config.id,
        interface_name: dbConfig.name,
        test_url: testUrl,
        status_code: response.status,
        response_time: Date.now() - testStartTime,
        test_time: new Date().toISOString(),
        response_data: data,
        data_count: dataCount,
        success: isSuccess,
        error_message: isSuccess ? null : '响应成功但无有效数据结构',
        test_params: { page: 1, size: 5 },
        test_headers: { 'Content-Type': 'application/json' },
        db_config: dbConfig
      };

      // 自动保存到数据库
      await interfaceConfigService.updateInterfaceConfig(config.id, {
        testStatus: isSuccess ? 'success' : 'failed',
        testData: JSON.stringify(testRecord),
        lastTestAt: new Date().toISOString()
      });

      // 返回测试结果，不抛出错误
      return { success: isSuccess, testRecord };

    } else {
      // HTTP状态码不是2xx
      const testRecord = {
        interface_id: config.id,
        interface_name: config.name,
        test_url: testUrl,
        status_code: response.status,
        response_time: Date.now() - testStartTime,
        test_time: new Date().toISOString(),
        response_data: null,
        data_count: 0,
        success: false,
        error_message: `HTTP ${response.status}`,
        test_params: { page: 1, size: 5 },
        test_headers: { 'Content-Type': 'application/json' }
      };

      // 保存失败记录
      await interfaceConfigService.updateInterfaceConfig(config.id, {
        testStatus: 'failed',
        testData: JSON.stringify(testRecord),
        lastTestAt: new Date().toISOString()
      });

      // 返回失败结果，不抛出错误
      return { success: false, testRecord };
    }

    } catch (fetchError: any) {
      // 处理fetch请求的错误（超时、网络错误等）
      clearTimeout(timeoutId);

      const errorMessage = fetchError.name === 'AbortError' ? '请求超时(10秒)' : fetchError.message;

      const testRecord = {
        interface_id: config.id,
        interface_name: config.name,
        test_url: testUrl,
        status_code: 0,
        response_time: Date.now() - testStartTime,
        test_time: new Date().toISOString(),
        response_data: null,
        data_count: 0,
        success: false,
        error_message: errorMessage,
        test_params: { page: 1, size: 5 },
        test_headers: { 'Content-Type': 'application/json' }
      };

      // 保存超时/网络错误记录
      await interfaceConfigService.updateInterfaceConfig(config.id, {
        testStatus: 'failed',
        testData: JSON.stringify(testRecord),
        lastTestAt: new Date().toISOString()
      });

      return { success: false, testRecord };
    }

  } catch (error: any) {
    // 清理超时定时器
    if (typeof timeoutId !== 'undefined') {
      clearTimeout(timeoutId);
    }
    // 构造异常的测试记录
    const currentHost = window.location.hostname;
    const fallbackUrl = `http://${currentHost}:8000/api/dynamic${config.path}?page=1&size=5`;

    // 检测是否是超时错误
    const errorMessage = error.name === 'AbortError' ? '请求超时(10秒)' : error.message;

    const testRecord = {
      interface_id: config.id,
      interface_name: config.name,
      test_url: fallbackUrl,
      status_code: 0,
      response_time: Date.now() - testStartTime,
      test_time: new Date().toISOString(),
      response_data: null,
      data_count: 0,
      success: false,
      error_message: errorMessage,
      test_params: { page: 1, size: 5 },
      test_headers: { 'Content-Type': 'application/json' }
    };

    // 保存失败记录
    await interfaceConfigService.updateInterfaceConfig(config.id, {
      testStatus: 'failed',
      testData: JSON.stringify(testRecord),
      lastTestAt: new Date().toISOString()
    });

    // 返回失败结果，不抛出错误
    return { success: false, testRecord };
  } finally {
    testingItems.value.delete(config.id);
  }
};

// 删除接口
const handleDelete = (row: InterfaceConfig, event?: Event) => {
  if (event) {
    event.stopPropagation(); // 阻止事件冒泡
  }

  deleteItem.value = row;
  deleteDialogVisible.value = true;
};

// 加载下拉选项数据
const loadOptions = async () => {
  try {
    // 并行加载所有选项数据（限制在100以内）
    const [groupsResponse, tagsResponse, dataSourcesResponse] = await Promise.all([
      interfaceGroupService.getInterfaceGroups({ page: 1, pageSize: 100 }),
      interfaceTagService.getInterfaceTags({ page: 1, page_size: 100 }),
      dataSourceService.getDataSources(1, 100)
    ]);

    interfaceGroups.value = groupsResponse.items;
    interfaceTags.value = tagsResponse.items;
    dataSources.value = dataSourcesResponse.items;
  } catch (error) {
    // 加载选项数据失败，继续使用空数组
  }
};

// 重新测试接口（供抽屉调用）
const retestInterface = (interfaceConfig: InterfaceConfig) => {
  handleTest(interfaceConfig);
};

// 页面加载时注册刷新机制
onMounted(() => {
  loadOptions();
  loadInterfaceConfigs();

  // 使用工具类注册刷新机制
  pageRefreshUtil.registerRefresh(
    PAGE_KEYS.INTERFACE_CONFIG,
    loadInterfaceConfigs,           // 保持当前页刷新
    loadInterfaceConfigsToFirstPage // 跳转第一页刷新
  );

  // 暴露刷新接口列表的方法供抽屉调用
  (window as any).refreshInterfaceList = async () => {
    await loadInterfaceConfigs();
  };

  // 暴露重新测试方法供抽屉调用
  (window as any).retestInterface = retestInterface;

  // 暴露保存测试状态方法供抽屉调用
  (window as any).saveTestResult = async (interfaceId: number, status: 'success' | 'failed') => {
    try {
      // 调用保存方法
      await updateInterfaceTestStatus(interfaceId, status);

      // 保存完成后刷新页面数据
      setTimeout(async () => {
        await loadInterfaceConfigs();
      }, 300);
      return true;
    } catch (error) {
      return false;
    }
  };

  // 暴露刷新接口列表的方法供抽屉调用
  (window as any).refreshInterfaceList = async () => {
    await loadInterfaceConfigs();
  };

  // 添加调试信息
  // 暴露直接测试数据库写入的方法
  (window as any).testDirectDbWrite = async (interfaceId: number) => {
    try {
      // 构造最简单的更新数据
      const testData = {
        testStatus: 'success',
        lastTestAt: new Date().toISOString()
      };
      // 直接调用API
      const url = `http://${window.location.hostname}:8000/api/v1/interface/configs/${interfaceId}`;
      const response = await fetch(url, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          // 只发送测试状态相关字段
          test_status: 'success',
          last_test_at: new Date().toISOString()
        })
      });
      if (!response.ok) {
        const errorText = await response.text();
        return { success: false, error: errorText };
      }

      const result = await response.json();
      return { success: true, data: result };
    } catch (error: any) {
      return { success: false, error: error.message || String(error) };
    }
  };
});

// 显示测试结果抽屉
const showTestResultDrawer = (interfaceConfig: InterfaceConfig, testRecord: any, isSuccess: boolean, mode: string = 'new') => {
  try {
    const title = mode === 'view' ? `查看测试记录 - ${interfaceConfig.name}` : `测试结果 - ${interfaceConfig.name}`;

    drawerMessenger.showDrawer({
      title: title,
      component: 'InterfaceTestResultForm',
      props: {
        interfaceConfig: interfaceConfig,
        testRecord: testRecord,
        isSuccess: isSuccess,
        viewMode: mode
      },
      size: '45%'
    });
  } catch (error) {
    ElMessage.error('打开测试结果抽屉失败');
  }
};

// 查看测试结果（点击列表中的测试状态）
const handleViewTestResult = async (row: InterfaceConfig) => {
  // 只有成功和失败状态才能打开抽屉
  if (!row.testStatus || (row.testStatus !== 'success' && row.testStatus !== 'failed')) {
    ElMessage.info('该接口暂无测试记录，请先进行测试');
    return;
  }

  try {
    // 由于测试状态保存在interface_configs表中，我们直接使用当前接口的信息
    // 构造一个测试记录对象用于显示
    const testRecord = {
      interface_id: row.id,
      interface_name: row.name,
      interface_path: row.path,
      interface_method: row.method,
      test_url: `http://${window.location.hostname}:8000/api/dynamic${row.path}`,
      status_code: row.testStatus === 'success' ? 200 : 500,
      response_time: 0, // 这些详细信息在当前架构下无法获取
      test_time: row.lastTestAt || new Date().toISOString(),
      response_data: row.testStatus === 'success' ? { message: '测试成功' } : { error: '测试失败' },
      data_count: 0,
      success: row.testStatus === 'success',
      error_message: row.testStatus === 'failed' ? '接口测试失败' : null,
      test_params: { page: 1, size: 5 },
      test_headers: { 'Content-Type': 'application/json' }
    };
    // 显示测试结果抽屉（查看模式）
    showTestResultDrawer(row, testRecord, row.testStatus === 'success', 'view');

  } catch (error) {
    ElMessage.error('查看测试结果失败');
  }
};

// 组件卸载时清理
onUnmounted(() => {
  pageRefreshUtil.unregisterRefresh(PAGE_KEYS.INTERFACE_CONFIG);
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;

/* 自定义按钮间距 - 减小搜索栏按钮间距 */
.header-actions {
  gap: 8px !important; /* 从默认的12px减小到8px */
}

/* 接口名称样式 */
.interface-name {
  display: flex;
  flex-direction: column;
  gap: 6px;
  text-align: left;
}

.name-text {
  font-weight: 500;
  color: #303133;
  word-break: break-word;
}

.interface-tags {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
}

.path-text {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  color: #606266;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
}

/* 分组和数据源样式 */
.group-text,
.datasource-text {
  color: #606266;
  font-size: 13px;
}

/* 测试状态样式 */
.test-status {
  display: flex;
  justify-content: center;
  align-items: center;

  .no-test {
    color: #C0C4CC;
    font-size: 12px;
  }

  .clickable-tag {
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }
}

/* 时间文本样式 */
.time-text {
  color: #909399;
  font-size: 13px;
}

/* 操作按钮容器样式 */
.action-buttons {
  display: flex;
  gap: 6px;
  justify-content: flex-start;
  align-items: center;
}

/* 合并列样式 */
.route-info {
  display: flex;
  align-items: center;
  gap: 0;
}

.group-datasource-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.group-name,
.datasource-name {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.group-name .el-icon {
  font-size: 12px;
  color: #3FC8DD;
}

.datasource-name .el-icon {
  font-size: 12px;
  color: #909399;
}

.group-text,
.datasource-text {
  color: #606266;
  font-size: 12px;
}

.status-permission-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: center;
}

.status-row,
.permission-row {
  display: flex;
  justify-content: center;
}
/* 批量测试进度条样式 */
.batch-test-progress {
  margin: 16px 0 24px 0;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border-left: 4px solid #409eff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .progress-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .progress-title {
      font-size: 16px;
      font-weight: 600;
      color: #409eff;
      display: flex;
      align-items: center;
      gap: 8px;

      .rotating {
        animation: rotate 1s linear infinite;
      }
    }
  }

  .el-progress {
    margin-bottom: 12px;

    :deep(.el-progress-bar__outer) {
      border-radius: 6px;
    }

    :deep(.el-progress-bar__inner) {
      border-radius: 6px;
    }
  }

  .progress-text {
    font-weight: 600;
    color: #606266;
  }

  .progress-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 14px;
    color: #909399;

    .current-batch {
      font-weight: 500;
    }

    .success-count {
      color: #67c23a;
      font-weight: 600;
    }

    .fail-count {
      color: #f56c6c;
      font-weight: 600;
    }
  }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 卡片视图样式 */
.card-container {
  .empty-state {
    grid-column: 1 / -1;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
  }
}

/* 接口配置卡片内容样式 - 与数据源页面保持一致 */
.interface-card-content {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .card-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
      flex: 1;
      margin-right: 12px;
      /* 卡片标题单行显示规则 */
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .status-tag {
      .status-icon {
        margin-right: 4px;
      }
    }
  }

  /* 接口信息：分行显示 */
  .interface-info {
    .info-row {
      display: flex;
      align-items: center;
      margin-bottom: 6px; // 减小行间距，因为现在是分行显示

      &:last-child {
        margin-bottom: 0; // 最后一行不需要下边距
      }

      .info-item {
        display: flex;
        align-items: center;
        font-size: 13px;
        width: 100%; // 每行占满宽度

        .info-icon {
          margin-right: 6px;
          color: #909399;
          font-size: 14px;
          flex-shrink: 0;
        }

        .info-label {
          font-size: 13px;
          color: #606266;
          margin-right: 8px;
          font-weight: 500;
        }

        .group-name {
          margin-left: 8px; // 与路由地址左对齐
          /* 分组名称溢出处理 */
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          cursor: help; /* 提示用户可以悬停查看完整内容 */
          flex: 1; // 占据剩余空间
        }

        .info-text {
          color: #606266;
        }

        .route-path {
          font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
          font-size: 12px;
          color: #606266;
          background: #f5f7fa;
          padding: 2px 6px;
          border-radius: 3px;
          margin-left: 6px;
          flex: 1; // 路径占据剩余空间
          /* 路由地址单行显示规则 - 超出显示省略号，悬停显示完整内容 */
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          cursor: help; /* 提示用户可以悬停查看完整内容 */
        }

        .tag-list {
          display: flex;
          gap: 4px;
          flex-wrap: wrap;
        }

        .no-tags {
          color: #909399;
          font-size: 12px;
          font-style: italic;
        }
      }
    }
  }

  /* 接口配置页面专用：操作栏左右分布布局 - 使用最高优先级覆盖BaseCard样式 */
  .interface-config-page :deep(.base-card .card-footer) {
    justify-content: flex-start !important; // 覆盖BaseCard的右对齐
  }

  .interface-config-page :deep(.base-card .card-footer .card-actions),
  .interface-config-page :deep(.base-card .card-footer .default-actions) {
    justify-content: flex-start !important; // 覆盖BaseCard兼容性样式的右对齐
  }

  .action-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .status-left {
    display: flex;
    align-items: center;
  }

  .actions-right {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .test-status-tag {
    font-size: 12px;
  }
}

</style>
