"""
接口分组业务逻辑层
处理接口分组相关的业务逻辑
"""

import math
from typing import Optional
from sqlalchemy.orm import Session
from app.config.interface.repositories.interface_group_repository import InterfaceGroupRepository
from app.config.interface.schemas.interface_group_schema import (
    InterfaceGroupCreate,
    InterfaceGroupUpdate,
    InterfaceGroupResponse,
    InterfaceGroupListResponse
)
from app.shared.core.exception_handler import BusinessException, TechnicalException
from app.shared.core.base_response import ErrorType
from app.shared.core.log_util import LogUtil


class InterfaceGroupService:
    """接口分组业务逻辑类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.repository = InterfaceGroupRepository(db)
        LogUtil.debug("接口分组服务初始化", service="InterfaceGroupService")
    
    def get_interface_groups(
        self, 
        page: int = 1, 
        size: int = 10, 
        search: Optional[str] = None
    ) -> InterfaceGroupListResponse:
        """
        获取接口分组列表
        
        Args:
            page: 页码
            size: 每页大小
            search: 搜索关键词
            
        Returns:
            接口分组列表响应
        """
        try:
            LogUtil.debug("开始获取接口分组列表", 
                         operation="get_interface_groups",
                         page=page, 
                         size=size, 
                         search=search)
            
            # 参数验证
            if page < 1:
                raise BusinessException(
                    user_message="页码必须大于0",
                    user_detail={"page": page},
                    error_type=ErrorType.验证错误
                )
            
            if size < 1 or size > 100:
                raise BusinessException(
                    user_message="每页大小必须在1-100之间",
                    user_detail={"size": size},
                    error_type=ErrorType.验证错误
                )
            
            # 获取数据
            items, total = self.repository.get_list(page, size, search)
            
            # 转换为响应格式
            groups = []
            for item in items:
                # 计算该分组下的接口数量
                interface_count = self.repository.get_interface_count_by_group_id(item.id)
                group_response = InterfaceGroupResponse.from_orm(item)
                group_response.interface_count = interface_count
                groups.append(group_response)
            
            # 计算总页数
            pages = math.ceil(total / size) if total > 0 else 0
            
            LogUtil.info("接口分组列表获取成功", 
                        operation="get_interface_groups",
                        total_count=total,
                        returned_count=len(groups),
                        page=page,
                        size=size)
            
            return InterfaceGroupListResponse(
                items=groups,
                total=total,
                page=page,
                size=size,
                pages=pages
            )
            
        except BusinessException:
            raise
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "get_interface_groups",
                    "error": str(e),
                    "params": {
                        "page": page, 
                        "size": size, 
                        "search": search
                    }
                }
            )
    
    def get_interface_group(self, group_id: int) -> InterfaceGroupResponse:
        """
        获取单个接口分组
        
        Args:
            group_id: 接口分组ID
            
        Returns:
            接口分组响应
        """
        try:
            LogUtil.debug("开始获取接口分组详情", 
                         operation="get_interface_group",
                         group_id=group_id)
            
            group = self.repository.get_by_id(group_id)
            if not group:
                raise BusinessException(
                    user_message="接口分组不存在",
                    user_detail={"group_id": group_id},
                    error_type=ErrorType.资源未找到
                )
            
            # 转换为响应格式
            interface_count = self.repository.get_interface_count_by_group_id(group.id)
            group_response = InterfaceGroupResponse.from_orm(group)
            group_response.interface_count = interface_count
            
            LogUtil.info("接口分组详情获取成功", 
                        operation="get_interface_group",
                        group_id=group_id,
                        name=group.name)
            
            return group_response
            
        except BusinessException:
            raise
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "get_interface_group",
                    "error": str(e),
                    "group_id": group_id
                }
            )
    
    def create_interface_group(self, group_data: InterfaceGroupCreate) -> InterfaceGroupResponse:
        """
        创建接口分组
        
        Args:
            group_data: 接口分组创建数据
            
        Returns:
            创建的接口分组响应
        """
        try:
            LogUtil.debug("开始创建接口分组",
                         operation="create_interface_group",
                         name=group_data.name,
                         path_prefix=group_data.path_prefix)
            
            # 检查名称是否已存在
            if self.repository.check_name_exists(group_data.name):
                raise BusinessException(
                    user_message="接口分组名称已存在",
                    user_detail={
                        "name": group_data.name,
                        "suggestion": "请使用其他名称"
                    },
                    error_type=ErrorType.资源冲突
                )
            
            # 检查路径前缀是否已存在
            if self.repository.check_path_prefix_exists(group_data.pathPrefix):
                raise BusinessException(
                    user_message="路径前缀已存在",
                    user_detail={
                        "pathPrefix": group_data.pathPrefix,
                        "suggestion": "请使用其他路径前缀"
                    },
                    error_type=ErrorType.资源冲突
                )
            
            # 创建接口分组
            group = self.repository.create(group_data)

            # 转换为响应格式
            group_response = InterfaceGroupResponse.from_orm(group)
            group_response.interface_count = 0  # TODO: 计算接口数量
            
            LogUtil.info("接口分组创建成功", 
                        operation="create_interface_group",
                        group_id=group.id,
                        name=group.name,
                        path_prefix=group.path_prefix)
            
            return group_response
            
        except BusinessException:
            raise
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "create_interface_group",
                    "error": str(e),
                    "group_data": group_data.dict()
                }
            )
    
    def update_interface_group(
        self, 
        group_id: int, 
        group_data: InterfaceGroupUpdate
    ) -> InterfaceGroupResponse:
        """
        更新接口分组
        
        Args:
            group_id: 接口分组ID
            group_data: 更新数据
            
        Returns:
            更新后的接口分组响应
        """
        try:
            LogUtil.debug("开始更新接口分组", 
                         operation="update_interface_group",
                         group_id=group_id)
            
            # 检查接口分组是否存在
            existing_group = self.repository.get_by_id(group_id)
            if not existing_group:
                raise BusinessException(
                    user_message="接口分组不存在",
                    user_detail={"group_id": group_id},
                    error_type=ErrorType.资源未找到
                )
            
            # 检查名称冲突
            if group_data.name and self.repository.check_name_exists(group_data.name, group_id):
                raise BusinessException(
                    user_message="接口分组名称已存在",
                    user_detail={
                        "name": group_data.name,
                        "suggestion": "请使用其他名称"
                    },
                    error_type=ErrorType.资源冲突
                )
            
            # 检查路径前缀冲突
            if group_data.path_prefix and self.repository.check_path_prefix_exists(group_data.path_prefix, group_id):
                raise BusinessException(
                    user_message="路径前缀已存在",
                    user_detail={
                        "path_prefix": group_data.path_prefix,
                        "suggestion": "请使用其他路径前缀"
                    },
                    error_type=ErrorType.资源冲突
                )
            
            # 更新接口分组
            updated_group = self.repository.update(group_id, group_data)
            
            # 转换为响应格式
            group_response = InterfaceGroupResponse.from_orm(updated_group)
            group_response.interface_count = 0  # TODO: 计算接口数量
            
            LogUtil.info("接口分组更新成功", 
                        operation="update_interface_group",
                        group_id=group_id,
                        name=updated_group.name)
            
            return group_response
            
        except BusinessException:
            raise
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "update_interface_group",
                    "error": str(e),
                    "group_id": group_id,
                    "group_data": group_data.dict()
                }
            )
    
    def delete_interface_group(self, group_id: int) -> dict:
        """
        删除接口分组
        
        Args:
            group_id: 接口分组ID
            
        Returns:
            删除结果
        """
        try:
            LogUtil.debug("开始删除接口分组", 
                         operation="delete_interface_group",
                         group_id=group_id)
            
            # 检查接口分组是否存在
            existing_group = self.repository.get_by_id(group_id)
            if not existing_group:
                raise BusinessException(
                    user_message="接口分组不存在",
                    user_detail={"group_id": group_id},
                    error_type=ErrorType.资源未找到
                )
            
            # TODO: 检查是否有关联的接口配置
            # 如果有关联的接口配置，应该提示用户先删除或转移接口配置
            
            # 删除接口分组
            success = self.repository.delete(group_id)
            
            if success:
                LogUtil.info("接口分组删除成功", 
                            operation="delete_interface_group",
                            group_id=group_id,
                            name=existing_group.name)
                
                return {"message": "接口分组删除成功"}
            else:
                raise TechnicalException(
                    error_type=ErrorType.操作失败,
                    developer_detail={
                        "operation": "delete_interface_group",
                        "group_id": group_id,
                        "error": "删除操作返回失败"
                    }
                )
            
        except BusinessException:
            raise
        except Exception as e:
            raise TechnicalException(
                error_type=ErrorType.数据库错误,
                developer_detail={
                    "operation": "delete_interface_group",
                    "error": str(e),
                    "group_id": group_id
                }
            )
