# 卡片组件开发规范

## 🎯 核心原则

### 1. 架构分离原则
- **公共组件**：BaseCard 放在 `src/components/common/` 目录下，提供统一的容器和基础样式
- **自定义内容**：具体的卡片内容和样式放在 `src/views/` 层下面，与业务逻辑一起管理
- **职责分离**：BaseCard 负责布局和通用功能，业务组件负责具体内容展示

### 2. 插槽机制
- 使用 Vue 3 插槽系统实现内容自定义
- 支持 `header`、`content`、`actions` 三个主要插槽
- 插槽提供 `item` 和 `config` 参数供业务组件使用

## 🏗️ BaseCard 公共组件规范

### 1. 组件属性 (Props)

```typescript
interface Props {
  item: any; // 数据项
  config?: {
    titleField?: string; // 标题字段名
    statusField?: string; // 状态字段名
    size?: 'small' | 'medium' | 'large'; // 卡片尺寸
    shadow?: 'always' | 'hover' | 'never'; // 阴影效果
    style?: 'compact-info'; // 卡片样式风格（仅支持紧凑信息样式）
  };
  showDefaultHeader?: boolean; // 是否显示默认头部
  showDefaultActions?: boolean; // 是否显示默认操作按钮
}
```

### 2. 固定网格布局标准

BaseCard组件提供了统一的固定网格布局样式 `.base-card-grid`，严格按照以下标准：

**卡片视图（15条记录）**：
- **手机端** (≤768px)：1列×15行
- **平板端** (769px-1024px)：3列×5行
- **桌面端** (≥1025px)：**固定5列×3行**

**卡片尺寸标准**：
- **固定高度**：180px
- **固定宽度**：由网格自动分配
- **间距**：16px（桌面端），20px（超大屏幕）

**使用方法：**
```vue
<div class="card-grid base-card-grid">
  <BaseCard v-for="item in items" :key="item.id" :item="item" />
</div>
```

### 2. 组件事件 (Emits)

```typescript
interface Emits {
  edit: [item: any, event: Event];
  test: [item: any, event: Event];
  delete: [item: any, event: Event];
}
```

### 3. 插槽定义

```vue
<!-- 卡片头部插槽 -->
<slot name="header" :item="item">
  <!-- 默认头部内容 -->
</slot>

<!-- 卡片内容插槽 -->
<slot name="content" :item="item" :config="config">
  <!-- 默认内容 -->
</slot>

<!-- 卡片操作插槽 -->
<slot name="actions" :item="item">
  <!-- 默认操作按钮 -->
</slot>
```

## 🎨 样式系统规范

### 1. 尺寸变体
- **small**: 紧凑型卡片，适用于密集展示
- **medium**: 标准型卡片，通用场景
- **large**: 大型卡片，适用于重要信息展示

### 2. 阴影效果
- **always**: 始终显示阴影
- **hover**: 悬停时显示阴影
- **never**: 不显示阴影

### 3. 样式风格
- **compact-info**: 紧凑信息风格（唯一支持的样式）
  - 浅灰色背景 (#fafafa)
  - 简洁边框设计
  - 紧凑的内边距布局
  - 悬停时背景变为白色

## 📝 使用规范

### 1. 基础使用示例

```vue
<template>
  <BaseCard
    :item="dataSource"
    :config="{
      titleField: 'name',
      statusField: 'status',
      size: 'small',
      shadow: 'hover',
      style: 'compact-info'
    }"
    :show-default-header="false"
    :show-default-actions="false"
    @click="handleView(dataSource)"
    @edit="(item, event) => handleEdit(item, event)"
    @test="(item, event) => handleTest(item, event)"
    @delete="(item, event) => handleDelete(item, event)"
  >
    <template #content="{ item }">
      <!-- 自定义卡片内容 -->
      <div class="custom-card-content">
        <h4>{{ item.name }}</h4>
        <p>{{ item.description }}</p>
      </div>
    </template>

    <template #actions="{ item }">
      <!-- 自定义操作按钮 -->
      <div class="card-actions">
        <el-button @click="handleEdit(item, $event)">编辑</el-button>
        <el-button @click="handleDelete(item, $event)">删除</el-button>
      </div>
    </template>
  </BaseCard>
</template>
```

### 2. 事件处理规范

```typescript
// 防止事件冒泡的正确处理方式
const handleEdit = (row: DataSource, event?: Event) => {
  if (event) {
    event.stopPropagation(); // 阻止事件冒泡
  }
  // 编辑逻辑
};

const handleView = (row: DataSource) => {
  // 查看逻辑（卡片点击事件）
};
```

### 3. 样式配置

```typescript
// 统一使用紧凑信息样式
const getCardStyle = () => {
  return 'compact-info';
};
```

## 🔧 自定义内容规范

### 1. 内容结构建议

```vue
<template #content="{ item }">
  <div class="datasource-card-content">
    <!-- 卡片头部：标题 + 状态 -->
    <div class="card-header">
      <h4 class="datasource-title">{{ item.name }}</h4>
      <el-tag :type="getStatusType(item.status)" size="small">
        {{ getStatusText(item.status) }}
      </el-tag>
    </div>

    <!-- 连接信息 -->
    <div class="connection-info">
      <div class="info-row">
        <div class="info-item">
          <el-icon><Connection /></el-icon>
          <span>{{ item.host }}:{{ item.port }}</span>
        </div>
      </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="status-bar">
      <el-tag>{{ item.dbType }}</el-tag>
      <span class="time-text">{{ formatTime(item.updatedAt) }}</span>
    </div>
  </div>
</template>
```

### 2. 样式命名规范

```scss
// 业务组件样式应使用具体的业务前缀
.datasource-card-content {
  .card-header { /* 头部样式 */ }
  .connection-info { /* 连接信息样式 */ }
  .status-bar { /* 状态栏样式 */ }
}

// 避免与BaseCard的通用样式冲突
.card-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}
```

## ⚡ 性能优化规范

### 1. 样式切换优化
- 使用计算属性缓存样式类名
- 避免在模板中进行复杂计算
- 合理使用 v-show 和 v-if

### 2. 事件处理优化
- 正确使用事件修饰符
- 避免不必要的事件监听
- 及时清理事件监听器

## 🚨 注意事项

### 1. 事件冒泡处理
- 操作按钮必须阻止事件冒泡：`@click="handleEdit(item, $event)"`
- 在事件处理函数中调用：`event.stopPropagation()`

### 2. 样式隔离
- 业务样式使用 `scoped` 属性
- 避免修改 BaseCard 的基础样式
- 使用深度选择器时要谨慎：`:deep(.class-name)`

### 3. 响应式设计
- 卡片网格使用 CSS Grid 或 Flexbox
- 考虑不同屏幕尺寸的适配
- 合理设置最小宽度和最大宽度

## 📋 检查清单

在使用卡片组件前，请确保：

- [ ] BaseCard 组件已正确导入
- [ ] 配置对象包含必要的字段映射
- [ ] 事件处理函数正确阻止冒泡
- [ ] 自定义样式使用了业务前缀
- [ ] 插槽内容结构清晰合理
- [ ] 响应式布局适配不同屏幕
- [ ] 空状态处理完善
- [ ] 加载状态显示正确

## 🔄 扩展指南

### 1. 修改卡片样式
1. 在 BaseCard.vue 中修改 `.style-compact-info` 样式类
2. 确保样式修改不影响现有布局
3. 在文档中更新样式说明

### 2. 添加新的插槽
1. 在 BaseCard 模板中定义新插槽
2. 更新使用示例和文档
3. 考虑向后兼容性

### 3. 扩展配置选项
1. 更新 Props 接口定义
2. 在组件中实现新配置的逻辑
3. 提供默认值和类型检查
