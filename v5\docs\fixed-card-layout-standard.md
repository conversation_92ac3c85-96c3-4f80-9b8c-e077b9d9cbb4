# Copyright (c) 2025 左岚. All rights reserved.

# 固定卡片布局标准

## 🎯 核心标准

### 卡片布局规范
- **卡片视图**：每页15个卡片，严格按照3行×5列布局
- **列表视图**：每页10条记录，表格形式
- **卡片尺寸**：固定高度180px，宽度由网格自动分配

## 📐 布局规格

### 网格布局标准
```scss
// 桌面端及以上：固定5列布局，3行（15个卡片）
@media (min-width: 1025px) {
  grid-template-columns: repeat(5, 1fr);
  gap: 16px;
  max-width: 1400px;
  margin: 0 auto 24px;
}
```

### 卡片尺寸标准
```scss
.base-card {
  width: 100%;
  height: 180px; // 固定高度，确保所有卡片高度一致
}
```

### 响应式断点
- **手机端** (≤768px)：1列×15行
- **平板端** (769px-1024px)：3列×5行
- **桌面端** (≥1025px)：**固定5列×3行**

## 🔧 技术实现

### 1. BaseCard组件修改

**固定尺寸**：
- 高度：180px
- 宽度：100%（由网格控制）
- 布局：flex纵向布局

**样式特性**：
```scss
&.style-compact-info {
  padding: 12px;
  display: flex;
  flex-direction: column;
  height: 180px;
  
  .card-content {
    flex: 1;
    overflow: hidden;
  }
}
```

### 2. 网格布局优化

**固定列数**：
- 移除auto-fill和minmax，使用固定5列
- 确保在桌面端始终显示5列×3行
- 限制最大宽度，防止卡片过宽

### 3. 分页标准配合

**视图模式自动切换**：
```typescript
watch(viewMode, (newMode) => {
  pageSize.value = newMode === 'card' ? 15 : 10;
  currentPage.value = 1;
  loadData();
});
```

## ✅ 实施效果

### 1. 布局一致性
- ✅ 所有卡片高度完全一致（180px）
- ✅ 桌面端严格5列×3行布局
- ✅ 15个卡片完美填满网格

### 2. 视觉效果
- ✅ 卡片间距统一（16px）
- ✅ 整体布局居中对齐
- ✅ 响应式适配不同屏幕

### 3. 用户体验
- ✅ 每页15个卡片，信息密度适中
- ✅ 固定布局，用户习惯一致
- ✅ 快速浏览，操作便捷

## 📋 应用范围

### 已实施页面
- ✅ 数据源管理页面
- ✅ 接口分组管理页面

### 标准模板
```vue
<template>
  <div class="card-grid base-card-grid">
    <BaseCard
      v-for="item in items"
      :key="item.id"
      :item="item"
      :config="{
        size: 'small',
        shadow: 'hover',
        style: 'compact-info'
      }"
    >
      <template #content="{ item }">
        <!-- 卡片内容，高度自适应 -->
      </template>
    </BaseCard>
  </div>
</template>
```

## 🚀 后续模块

### 开发指南
1. **直接使用**：新模块直接使用BaseCard组件
2. **无需修改**：网格布局和尺寸已标准化
3. **专注内容**：只需设计卡片内容布局

### 注意事项
- 卡片内容需适应180px固定高度
- 使用flex布局确保内容合理分布
- 避免内容溢出，使用overflow: hidden

## 📝 维护规范

### 禁止修改项
- ❌ 不允许修改卡片固定高度（180px）
- ❌ 不允许修改桌面端5列布局
- ❌ 不允许修改15条记录的分页标准

### 允许调整项
- ✅ 可以调整卡片内容布局
- ✅ 可以自定义卡片内部样式
- ✅ 可以添加特殊的业务样式

## 🔄 版本记录

**v1.0** - 2025年1月31日
- 建立固定卡片布局标准
- 实现3行×5列严格布局
- 统一卡片高度180px
- 应用于数据源和接口分组页面

---

**制定团队**：左岚团队  
**适用范围**：全系统卡片视图页面  
**强制执行**：所有新模块必须遵循此标准
