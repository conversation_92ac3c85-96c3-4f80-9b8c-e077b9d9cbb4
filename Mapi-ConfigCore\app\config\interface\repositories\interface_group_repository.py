"""
接口分组数据访问层
负责数据库的CRUD操作
"""

from typing import List, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from app.config.interface.models.interface_group_model import InterfaceGroupModel
from app.config.interface.models.interface_config_model import InterfaceConfigModel
from app.config.interface.schemas.interface_group_schema import InterfaceGroupCreate, InterfaceGroupUpdate
from app.shared.core.log_util import LogUtil


class InterfaceGroupRepository:
    """接口分组数据访问类"""
    
    def __init__(self, db: Session):
        self.db = db
        LogUtil.debug("接口分组Repository初始化", repository="InterfaceGroupRepository")
    
    def get_by_id(self, group_id: int) -> Optional[InterfaceGroupModel]:
        """根据ID获取接口分组"""
        LogUtil.debug("根据ID获取接口分组", group_id=group_id)
        return self.db.query(InterfaceGroupModel).filter(InterfaceGroupModel.id == group_id).first()
    
    def get_by_name(self, name: str) -> Optional[InterfaceGroupModel]:
        """根据名称获取接口分组"""
        LogUtil.debug("根据名称获取接口分组", name=name)
        return self.db.query(InterfaceGroupModel).filter(InterfaceGroupModel.name == name).first()
    
    def get_by_path_prefix(self, path_prefix: str) -> Optional[InterfaceGroupModel]:
        """根据路径前缀获取接口分组"""
        LogUtil.debug("根据路径前缀获取接口分组", path_prefix=path_prefix)
        return self.db.query(InterfaceGroupModel).filter(InterfaceGroupModel.path_prefix == path_prefix).first()
    
    def get_list(
        self, 
        page: int = 1, 
        size: int = 10, 
        search: Optional[str] = None
    ) -> Tuple[List[InterfaceGroupModel], int]:
        """
        获取接口分组列表（分页）
        
        Args:
            page: 页码
            size: 每页大小
            search: 搜索关键词（名称、路径前缀、描述）
            
        Returns:
            (接口分组列表, 总数量)
        """
        LogUtil.debug("获取接口分组列表", page=page, size=size, search=search)
        
        query = self.db.query(InterfaceGroupModel)
        
        # 搜索过滤
        if search:
            search_filter = or_(
                InterfaceGroupModel.name.ilike(f"%{search}%"),
                InterfaceGroupModel.path_prefix.ilike(f"%{search}%"),
                InterfaceGroupModel.description.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        items = query.order_by(InterfaceGroupModel.created_at.desc()).offset((page - 1) * size).limit(size).all()
        
        LogUtil.info("接口分组列表查询完成", total=total, returned_count=len(items))
        return items, total
    
    def create(self, group_data: InterfaceGroupCreate) -> InterfaceGroupModel:
        """
        创建接口分组
        
        Args:
            group_data: 接口分组创建数据
            
        Returns:
            创建的接口分组模型
        """
        LogUtil.debug("创建接口分组", name=group_data.name, path_prefix=group_data.path_prefix)
        
        # 创建接口分组模型
        db_group = InterfaceGroupModel(
            name=group_data.name,
            path_prefix=group_data.path_prefix,
            description=group_data.description,
            is_enabled=group_data.is_enabled,
            created_by=group_data.created_by
        )
        
        self.db.add(db_group)
        self.db.commit()
        self.db.refresh(db_group)
        
        LogUtil.info("接口分组创建成功", group_id=db_group.id, name=db_group.name)
        return db_group


    
    def update(self, group_id: int, group_data: InterfaceGroupUpdate) -> Optional[InterfaceGroupModel]:
        """
        更新接口分组
        
        Args:
            group_id: 接口分组ID
            group_data: 更新数据
            
        Returns:
            更新后的接口分组模型
        """
        LogUtil.debug("更新接口分组", group_id=group_id)
        
        db_group = self.get_by_id(group_id)
        if not db_group:
            LogUtil.warning("接口分组不存在", group_id=group_id)
            return None
        
        # 更新字段
        update_data = group_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_group, field, value)
        
        self.db.commit()
        self.db.refresh(db_group)
        
        LogUtil.info("接口分组更新成功", group_id=db_group.id, name=db_group.name)
        return db_group
    
    def delete(self, group_id: int) -> bool:
        """
        删除接口分组
        
        Args:
            group_id: 接口分组ID
            
        Returns:
            是否删除成功
        """
        LogUtil.debug("删除接口分组", group_id=group_id)
        
        db_group = self.get_by_id(group_id)
        if not db_group:
            LogUtil.warning("接口分组不存在", group_id=group_id)
            return False
        
        self.db.delete(db_group)
        self.db.commit()
        
        LogUtil.info("接口分组删除成功", group_id=group_id, name=db_group.name)
        return True
    
    def check_name_exists(self, name: str, exclude_id: Optional[int] = None) -> bool:
        """
        检查名称是否已存在
        
        Args:
            name: 接口分组名称
            exclude_id: 排除的ID（用于更新时检查）
            
        Returns:
            是否存在
        """
        query = self.db.query(InterfaceGroupModel).filter(InterfaceGroupModel.name == name)
        
        if exclude_id:
            query = query.filter(InterfaceGroupModel.id != exclude_id)
        
        exists = query.first() is not None
        LogUtil.debug("检查接口分组名称是否存在", name=name, exclude_id=exclude_id, exists=exists)
        return exists
    
    def check_path_prefix_exists(self, path_prefix: str, exclude_id: Optional[int] = None) -> bool:
        """
        检查路径前缀是否已存在
        
        Args:
            path_prefix: 路径前缀
            exclude_id: 排除的ID（用于更新时检查）
            
        Returns:
            是否存在
        """
        query = self.db.query(InterfaceGroupModel).filter(InterfaceGroupModel.path_prefix == path_prefix)
        
        if exclude_id:
            query = query.filter(InterfaceGroupModel.id != exclude_id)
        
        exists = query.first() is not None
        LogUtil.debug("检查路径前缀是否存在", path_prefix=path_prefix, exclude_id=exclude_id, exists=exists)
        return exists

    def get_interface_count_by_group_id(self, group_id: int) -> int:
        """
        获取指定分组下的接口数量

        Args:
            group_id: 分组ID

        Returns:
            该分组下的接口数量
        """
        LogUtil.debug("统计分组下的接口数量", group_id=group_id)

        count = self.db.query(InterfaceConfigModel).filter(
            InterfaceConfigModel.group_id == group_id
        ).count()

        LogUtil.debug("分组接口数量统计完成", group_id=group_id, count=count)
        return count
