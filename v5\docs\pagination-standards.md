# Copyright (c) 2025 左岚. All rights reserved.

# 分页标准规范

## 🎯 核心标准

### 分页数量标准
- **列表视图**：每页10条记录
- **卡片视图**：每页15条记录

### 设计理念
- **列表视图**：数据密度高，10条记录便于快速浏览和操作
- **卡片视图**：视觉展示丰富，15条记录在大屏幕上形成3行×5列的完美布局

## 📱 响应式布局效果

### 卡片视图（15条记录）
- **手机端** (≤768px)：1列×15行
- **平板端** (769px-1024px)：2列×8行（最后一行1个）
- **小桌面端** (1025px-1200px)：3列×5行
- **中桌面端** (1201px-1600px)：4列×4行（最后一行3个）
- **大屏幕** (≥1601px)：5列×3行

### 列表视图（10条记录）
- 所有屏幕尺寸：表格形式，每页10行数据

## 🔧 技术实现

### 1. 自动切换机制

```typescript
// 监听视图模式切换，自动调整分页大小
watch(viewMode, (newMode) => {
  if (newMode === 'card') {
    pageSize.value = 15; // 卡片视图：15条
  } else {
    pageSize.value = 10; // 列表视图：10条
  }
  currentPage.value = 1; // 重置到第一页
  loadData(); // 重新加载数据
});
```

### 2. 默认配置

```typescript
// 页面初始化（默认卡片视图）
const viewMode = ref<'card' | 'list'>('card');
const pageSize = ref(15); // 卡片视图默认15条
```

### 3. 分页组件配置

```typescript
// PaginationComponent 默认选项
pageSizes: () => [10, 15, 20, 50, 100]
// 10条：列表视图标准
// 15条：卡片视图标准
// 20、50、100：用户自定义选项
```

## 📋 实施清单

### 已实施页面
- ✅ 数据源管理页面
- ✅ 接口分组管理页面

### 待实施页面
- [ ] 接口配置管理页面
- [ ] 其他业务模块页面

## 🎨 视觉效果优势

### 卡片视图（15条）
1. **大屏幕完美布局**：3行×5列，视觉平衡
2. **减少翻页次数**：比10条多50%的内容展示
3. **保持响应式**：在各种屏幕尺寸下都有良好表现

### 列表视图（10条）
1. **快速浏览**：数据密度适中，不会过于拥挤
2. **操作便捷**：10行数据便于批量操作和选择
3. **加载性能**：数据量适中，加载速度快

## 🔄 后端适配

### API默认值保持10条
```python
# 后端路由保持10条默认值
size: int = Query(10, ge=1, le=100, description="每页大小")
```

### 前端动态调整
- 前端根据视图模式动态传递不同的size参数
- 后端无需修改，保持向后兼容

## 📝 使用规范

### 新建页面模板

```vue
<script setup lang="ts">
import { ref, watch } from 'vue';

// 视图模式（默认卡片视图）
const viewMode = ref<'card' | 'list'>('card');
const pageSize = ref(15); // 卡片视图默认15条

// 自动切换分页大小
watch(viewMode, (newMode) => {
  pageSize.value = newMode === 'card' ? 15 : 10;
  currentPage.value = 1;
  loadData();
});
</script>
```

### 分页组件使用

```vue
<PaginationComponent
  v-model:current-page="currentPage"
  v-model:page-size="pageSize"
  :total="totalCount"
  @size-change="handleSizeChange"
  @current-change="handleCurrentChange"
/>
```

## 🚀 未来扩展

### 可配置化
- 考虑将分页标准配置化，支持不同业务模块自定义
- 提供全局配置文件，统一管理分页标准

### 性能优化
- 大数据量场景下考虑虚拟滚动
- 智能预加载下一页数据

### 用户体验
- 记住用户的分页偏好设置
- 提供快速跳转到指定页码的功能

---

**制定时间：** 2025年1月31日  
**制定团队：** 左岚团队  
**适用范围：** 全系统列表和卡片页面
