<template>
  <div class="container">
    <div class="page-header">
      <div class="page-title">
        <el-icon><PriceTag /></el-icon>
        <span class="title-text">接口标签管理</span>
      </div>
      <div class="header-actions">
        <!-- 视图切换按钮 -->
        <div class="view-toggle">
          <el-button-group>
            <el-button
              :type="currentView === 'card' ? 'primary' : ''"
              @click="currentView = 'card'"
            >
              <el-icon><Grid /></el-icon>
            </el-button>
            <el-button
              :type="currentView === 'table' ? 'primary' : ''"
              @click="currentView = 'table'"
            >
              <el-icon><List /></el-icon>
            </el-button>
          </el-button-group>
        </div>

        <SearchComponent
          v-model="searchQuery"
          placeholder="搜索标签名称或描述"
          width="300px"
          @search="handleSearch"
          @clear="handleSearch"
        />
        <el-button type="primary" @click="handleAdd">新增标签</el-button>
        <el-button @click="loadInterfaceTags">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- 卡片视图 -->
    <div v-if="currentView === 'card'" class="page-container-unified">
      <div class="base-card-grid">
        <BaseCard
          v-for="tag in interfaceTags"
          :key="tag.id"
          :item="tag"
          :config="{
            titleField: 'name',
            statusField: 'isEnabled',
            size: 'small',
            shadow: 'hover',
            style: 'compact-info'
          }"
          :show-default-header="false"
          :show-default-actions="false"
          @click="handleView(tag)"
          @edit="(item, event) => handleEdit(item, event)"
          @delete="(item, event) => handleDelete(item, event)"
        >
          <template #content="{ item }">
            <!-- 卡片头部：标题 + 状态 -->
            <div class="card-header">
              <h4 class="card-title" :title="item.name">{{ item.name }}</h4>
              <el-tag :type="getStatusType(item.isEnabled)" size="small" class="status-tag">
                <el-icon class="status-icon">
                  <CircleCheck v-if="item.isEnabled" />
                  <CircleClose v-else />
                </el-icon>
                {{ item.isEnabled ? '启用' : '禁用' }}
              </el-tag>
            </div>

            <!-- 标签信息：分行显示 -->
            <div class="main-info">
              <!-- 第一行：标签颜色 -->
              <div class="info-row">
                <div class="info-item">
                  <el-icon class="info-icon"><Brush /></el-icon>
                  <span class="info-label">标签颜色</span>
                  <div class="color-display">
                    <div
                      class="color-block"
                      :style="{ backgroundColor: item.color }"
                    ></div>
                  </div>
                </div>
              </div>

              <!-- 第二行：接口数量 -->
              <div class="info-row">
                <div class="info-item">
                  <el-icon class="info-icon"><Link /></el-icon>
                  <span class="info-label">接口数量</span>
                  <span class="info-text interface-count">{{ item.interfaceCount || 0 }}</span>
                </div>
              </div>
            </div>

            <!-- 底部栏：标签类型 + 更新时间 -->
            <div class="status-bar">
              <el-tag type="info" size="small" class="tag-type-tag">
                接口标签
              </el-tag>
              <div class="update-time">
                <el-icon class="time-icon"><Clock /></el-icon>
                <span class="time-text">{{ formatDate(item.updatedAt) }}</span>
              </div>
            </div>
          </template>

          <template #actions="{ item }">
            <div class="card-actions">
              <el-button type="primary" size="small" circle @click="handleEdit(item, $event)" title="编辑">
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button size="small" type="danger" circle @click="handleDelete(item, $event)" title="删除">
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
          </template>
        </BaseCard>

        <!-- 空状态 -->
        <div v-if="interfaceTags.length === 0 && !loading" class="empty-state-container">
          <EmptyState
            :type="searchQuery ? 'search' : 'card'"
            :title="searchQuery ? '无搜索结果' : '暂无接口标签'"
            :description="searchQuery ? `没有找到包含「${searchQuery}」的接口标签，请尝试其他搜索条件` : '当前没有配置任何接口标签，您可以点击上方按钮添加新的接口标签'"
            :action-text="searchQuery ? '清除搜索' : '新增标签'"
            @action="searchQuery ? clearSearch : handleAdd"
          />
        </div>
      </div>
    </div>

    <!-- 表格视图 -->
    <div v-else-if="currentView === 'table'">
        <!-- 标签列表 -->
        <el-table
          v-loading="loading"
          :data="interfaceTags"
          style="width: 100%; min-width: 900px;"
          :row-style="{ height: '60px' }"
          :cell-style="{ padding: '12px 0' }"
          :scroll-x="true"
        >
          <el-table-column label="标签名称" prop="name" width="140">
            <template #default="{ row }">
              <div class="tag-name">
                <span class="name-text">{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="标签颜色" prop="color" width="140">
            <template #default="{ row }">
              <div class="color-display">
                <div
                  class="color-block"
                  :style="{ backgroundColor: row.color }"
                  @click="handleEditColor(row)"
                  title="点击修改颜色"
                ></div>
                <span class="color-text">{{ row.color }}</span>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="描述" prop="description" min-width="150" width="350">
            <template #default="{ row }">
              <span class="description-text">{{ row.description || '暂无描述' }}</span>
            </template>
          </el-table-column>

          <el-table-column label="状态" prop="isEnabled" width="80" align="center">
            <template #default="{ row }">
              <span :class="row.isEnabled ? 'status-enabled' : 'status-disabled'">
                {{ row.isEnabled ? '启用' : '禁用' }}
              </span>
            </template>
          </el-table-column>

          <el-table-column label="关联接口" prop="interfaceCount" width="150" align="center">
            <template #default="{ row }">
              <el-tag type="info" size="small">{{ row.interfaceCount || 0 }}</el-tag>
            </template>
          </el-table-column>

          <el-table-column label="创建时间" prop="createdAt" min-width="160" />
          <el-table-column label="更新时间" prop="updatedAt" min-width="160" />

          <el-table-column label="操作" width="140" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button type="primary" size="small" @click="handleEdit(row)">编辑</el-button>
                <el-button type="danger" size="small" @click="handleDelete(row)">删除</el-button>
              </div>
            </template>
          </el-table-column>

          <!-- 空状态插槽 -->
          <template #empty>
            <EmptyState
              :type="searchQuery ? 'search' : 'table'"
              :title="searchQuery ? '无搜索结果' : '暂无接口标签'"
              :description="searchQuery ? `没有找到包含「${searchQuery}」的接口标签，请尝试其他搜索条件` : '当前没有配置任何接口标签，您可以点击上方按钮添加新的接口标签'"
              :action-text="searchQuery ? '清除搜索' : '新增标签'"
              @action="searchQuery ? clearSearch : handleAdd"
            />
          </template>
        </el-table>

        <!-- 分页 -->
        <PaginationComponent
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="totalCount"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
    </div>

    <!-- 统一分页 -->
    <PaginationComponent
      v-if="currentView === 'card'"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="totalCount"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />



    <!-- 删除确认对话框 -->
    <ConfirmDialog
      v-model="deleteDialogVisible"
      title="删除标签"
      :content="`确定要删除标签 '${deleteItem?.name}' 吗？`"
      @confirm="confirmDelete"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import {
  PriceTag,
  Refresh,
  Grid,
  List,
  Edit,
  Delete,
  Brush,
  Document,
  Link,
  Clock,
  CircleCheck,
  CircleClose
} from '@element-plus/icons-vue';
import ConfirmDialog from '@/components/common/ConfirmDialog.vue';
import SearchComponent from '@/components/common/SearchComponent.vue';
import PaginationComponent from '@/components/common/PaginationComponent.vue';
import EmptyState from '@/components/common/EmptyState.vue';
import BaseCard from '@/components/common/BaseCard.vue';

import interfaceTagService from '@/services/interface-tag.service';
import type { InterfaceTag } from '@/types/interface-tag';
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger';
import { pageRefreshUtil, PAGE_KEYS } from '@/utils/pageRefreshUtil';

// 全局抽屉通信助手
const drawerMessenger = useGlobalDrawerMessenger();

// 视图状态管理
const currentView = ref<'card' | 'table'>('card'); // 默认卡片视图

// 监听视图模式切换，自动调整分页大小
watch(currentView, (newView) => {
  if (newView === 'card') {
    pageSize.value = 15; // 卡片视图：15条
  } else {
    pageSize.value = 10; // 表格视图：10条
  }
  currentPage.value = 1; // 重置到第一页
  loadInterfaceTags(); // 重新加载数据
});

// 列表数据
const loading = ref(false);
const interfaceTags = ref<InterfaceTag[]>([]);
const searchQuery = ref('');
const currentPage = ref(1);
const pageSize = ref(15); // 卡片视图默认15条，表格视图10条
const totalCount = ref(0);

// 删除确认相关
const deleteDialogVisible = ref(false);
const deleteItem = ref<InterfaceTag | null>(null);

// 辅助方法
const getStatusType = (isEnabled: boolean) => {
  return isEnabled ? 'success' : 'danger';
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN');
};

// 查看接口标签详情
const handleView = async (tag: InterfaceTag) => {
  try {
    // 从后端获取完整的接口标签信息
    const fullInterfaceTag = await interfaceTagService.getInterfaceTagById(tag.id);
    if (!fullInterfaceTag) {
      ElMessage.error('接口标签不存在或已被删除');
      return;
    }

    drawerMessenger.showDrawer({
      title: '查看接口标签',
      component: 'InterfaceTagForm',
      props: {
        isEdit: false, // 查看模式，不显示保存按钮
        isView: true,  // 标识为查看模式
        editData: fullInterfaceTag // 使用从后端获取的完整数据
      },
      size: '28%'
    });
  } catch (error) {
    console.error('获取接口标签详情失败:', error);
    ElMessage.error('获取接口标签详情失败');
  }
};



// 加载标签列表
const loadInterfaceTags = async () => {
  loading.value = true;
  try {
    const response = await interfaceTagService.getInterfaceTags({
      page: currentPage.value,
      page_size: pageSize.value,
      search: searchQuery.value || undefined
    });

    interfaceTags.value = response.items;
    totalCount.value = response.total;
  } catch (error) {
    console.error('加载接口标签列表失败:', error);
    ElMessage.error('加载接口标签列表失败');
  } finally {
    loading.value = false;
  }
};

// 刷新到第一页的方法
const loadInterfaceTagsToFirstPage = async () => {
  currentPage.value = 1;
  await loadInterfaceTags();
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  loadInterfaceTags();
};

// 清除搜索
const clearSearch = () => {
  searchQuery.value = '';
  handleSearch();
};

// 分页处理
const handleSizeChange = (size: number) => {
  pageSize.value = size;
  currentPage.value = 1;
  loadInterfaceTags();
};

const handleCurrentChange = (page: number) => {
  currentPage.value = page;
  loadInterfaceTags();
};

// 切换标签状态

// 新增标签
const handleAdd = () => {
  drawerMessenger.showDrawer({
    title: '新增标签',
    component: 'InterfaceTagForm',
    props: {
      isEdit: false,
      editData: null
    },
    size: '28%'
  });
};

// 编辑标签
const handleEdit = (row: InterfaceTag, event?: Event) => {
  if (event) {
    event.stopPropagation(); // 阻止事件冒泡
  }

  drawerMessenger.showDrawer({
    title: '编辑标签',
    component: 'InterfaceTagForm',
    props: {
      isEdit: true,
      editData: {
        id: row.id,
        name: row.name,
        color: row.color,
        description: row.description || '',
        isEnabled: row.isEnabled
      }
    },
    size: '28%'
  });
};

// 快速编辑颜色
const handleEditColor = (row: InterfaceTag) => {
  drawerMessenger.showDrawer({
    title: '编辑标签',
    component: 'InterfaceTagForm',
    props: {
      isEdit: true,
      editData: {
        id: row.id,
        name: row.name,
        color: row.color,
        description: row.description,
        isEnabled: row.isEnabled
      }
    },
    size: '28%'
  });
};

// 删除标签
const handleDelete = (row: InterfaceTag, event?: Event) => {
  if (event) {
    event.stopPropagation(); // 阻止事件冒泡
  }

  deleteItem.value = row;
  deleteDialogVisible.value = true;
};

const confirmDelete = async () => {
  if (!deleteItem.value) return;

  try {
    await interfaceTagService.deleteInterfaceTag(deleteItem.value.id);
    ElMessage.success(`标签 ${deleteItem.value.name} 已删除`);

    // 删除操作：跳转第一页刷新（最高效）
    loadInterfaceTagsToFirstPage();
  } catch (error: any) {
    console.error('删除失败:', error);
    ElMessage.error(error.message || '删除失败');
  } finally {
    deleteDialogVisible.value = false;
    deleteItem.value = null;
  }
};

// 页面加载时注册刷新机制
onMounted(() => {
  loadInterfaceTags();

  // 使用工具类注册刷新机制
  pageRefreshUtil.registerRefresh(
    PAGE_KEYS.INTERFACE_TAG,
    loadInterfaceTags,           // 保持当前页刷新
    loadInterfaceTagsToFirstPage // 跳转第一页刷新
  );
});

// 组件卸载时清理
onUnmounted(() => {
  pageRefreshUtil.unregisterRefresh(PAGE_KEYS.INTERFACE_TAG);
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;
/* 引入公共样式，但保留现有样式作为备份和覆盖 */

/* 接口标签管理页面特有样式 */
/* 注意：以下样式会覆盖公共样式，确保页面效果不变 */

/* 容器样式 - 2024-12-26: 已在page-common.scss中，注释保留备份 */
/* .container {
  margin: 10px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  overflow: auto;
} */

/* 页面头部样式 - 2024-12-26: 已在page-common.scss中，注释保留备份 */
/* .page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 500;
  color: #303133;
}

.page-title .el-icon {
  margin-right: 8px;
  font-size: 20px;
  color: #3FC8DD;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
} */

/* 页签样式 - 2024-12-26: 已在page-common.scss中，注释保留备份 */
/* .page-tabs {
  margin-top: 0;
}

:deep(.el-tabs__header) {
  margin-bottom: 15px;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__active-bar) {
  background-color: #3FC8DD;
}

:deep(.el-tabs__item.is-active) {
  color: #3FC8DD;
}

:deep(.el-tabs__item:hover) {
  color: #3FC8DD;
} */

/* 表格基础样式 - 2024-12-26: 已在page-common.scss中，注释保留备份 */
/* :deep(.el-table) {
  border: none;
}

:deep(.el-table__header th) {
  background: transparent;
  color: #303133;
  font-weight: bold;
  border-bottom: 1px solid #ebeef5;
  border-left: none;
  border-right: none;
  padding: 12px 0;
}

:deep(.el-table__body tr:hover) {
  background-color: #f5f7fa;
}

:deep(.el-table__body td) {
  padding: 12px 0;
  border-bottom: 1px solid #ebeef5;
  border-left: none;
  border-right: none;
} */

/* 操作按钮样式 - 2024-12-26: 已在page-common.scss中，注释保留备份 */
/* :deep(.el-button--small) {
  padding: 5px 11px;
  font-size: 12px;
  border-radius: 3px;
}

:deep(.el-button--primary) {
  color: #fff;
  background-color: #3FC8DD;
  border-color: #3FC8DD;
}

:deep(.el-button--primary:hover) {
  background-color: #35b3c7;
  border-color: #35b3c7;
} */

/* 分页容器样式 - 2024-12-26: 已改用PaginationComponent，无需自定义样式 */

/* 标签名称样式 */
.tag-name {
  display: flex;
  align-items: center;
}

.name-text {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

/* 颜色显示样式 */
.color-display {
  display: flex;
  align-items: center;
  gap: 10px;
}

.color-block {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  cursor: pointer;
  transition: all 0.2s;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.color-block:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.color-text {
  font-size: 12px;
  color: #606266;
  font-family: monospace;
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 3px;
}

/* 描述文本样式 */
.description-text {
  color: #606266;
  line-height: 1.4;
}

/* 时间文本样式 */
.time-text {
  color: #909399;
  font-size: 13px;
}

/* 操作按钮容器样式 */
.action-buttons {
  display: flex;
  gap: 8px;
}

/* 接口标签特有样式 */
.info-label {
  font-size: 13px;
  color: #606266;
  margin-right: 8px;
  font-weight: 500;
}

.interface-count {
  margin-left: 8px; // 与颜色方块左对齐
}

.color-display {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px; // 与标签文字保持间距

  .color-block {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .color-text {
    font-size: 12px;
    color: #606266;
    font-family: monospace;
    background: #f5f7fa;
    padding: 2px 6px;
    border-radius: 3px;
  }
}

/* BaseCard标题样式标准化 - 与数据源保持一致 */
.card-header {
  .card-title {
    margin: 0;
    margin-top: -2px; // 上移2px，与数据源保持一致
    font-size: 16px; // 统一字体大小
    font-weight: normal; // 强制非粗体，与数据源保持一致
    color: #303133; // 统一颜色
    text-align: left; // 标题左对齐
  }
}

/* 空状态容器 */
.empty-state-container {
  grid-column: 1 / -1;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

/* 操作按钮样式 - 2024-12-26: 按钮样式已在page-common.scss中定义，注释重复定义 */
/* .action-buttons .el-button {
  padding: 5px 12px;
  font-size: 12px;
} */

/* 2024-12-27: 颜色选择器样式已移至 InterfaceTagForm.vue */

/* 状态文字样式 */
/* 状态文字样式 - 2024-12-26: 已在page-common.scss中，注释保留备份 */
/* .status-enabled {
  color: #67c23a;
  font-weight: 500;
}

.status-disabled {
  color: #f56c6c;
  font-weight: 500;
} */

/* 表格宽度和布局修复 - 2024-12-26: 已在page-common.scss中，注释保留备份 */
/* :deep(.el-table__header) {
  width: 100% !important;
  min-width: fit-content !important;
}

:deep(.el-table__body) {
  width: 100% !important;
  min-width: fit-content !important;
}

:deep(.el-table__header-wrapper) {
  overflow-x: auto !important;
}

:deep(.el-table__body-wrapper) {
  overflow-x: auto !important;
}

:deep(.el-scrollbar__view) {
  display: block !important;
  width: 100% !important;
}

:deep(.el-table) {
  width: 100% !important;
  table-layout: auto !important;
} */

/* 表格滚动条样式 - 2024-12-26: 已在page-common.scss中，注释保留备份 */
/* :deep(.el-table) {
  overflow-y: hidden !important;
}

:deep(.el-table__header-wrapper) {
  overflow-y: hidden !important;
  overflow-x: auto !important;
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

:deep(.el-table__header-wrapper):hover {
  scrollbar-color: #9db7bd transparent;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar) {
  height: 6px;
  width: 0px !important;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar-thumb) {
  background: transparent;
  border-radius: 3px;
  transition: background 0.3s;
}

:deep(.el-table__header-wrapper):hover::-webkit-scrollbar-thumb {
  background: #9db7bd;
} */

/* :deep(.el-table__body-wrapper) {
  overflow-y: hidden !important;
  overflow-x: auto !important;
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

:deep(.el-table__body-wrapper):hover {
  overflow-y: hidden !important;
  scrollbar-color: #9db7bd transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 6px;
  width: 0px !important;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar:horizontal) {
  height: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar:vertical) {
  display: none !important;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: transparent;
  border-radius: 3px;
  transition: background 0.3s;
}

:deep(.el-table__body-wrapper):hover::-webkit-scrollbar-thumb {
  background: #9db7bd;
} */

/* 隐藏Element Plus内部滚动条组件 - 2024-12-26: 已在page-common.scss中，注释保留备份 */
/* :deep(.el-scrollbar__bar.is-vertical) {
  display: none !important;
}

:deep(.el-table .el-scrollbar__bar.is-vertical) {
  display: none !important;
}

:deep(.el-table:hover .el-scrollbar__bar.is-vertical) {
  display: none !important;
} */

/* 分页容器样式 - 2024-12-26: 重复定义，已在page-common.scss中 */

/* 对话框底部样式 - 2024-12-26: 已在page-common.scss中，注释保留备份 */
/* .dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
} */

/* 页面标题响应式样式 - 2024-12-26: 已在page-common.scss中，注释保留备份 */
/* .page-title {
  flex-wrap: wrap;
  min-width: 0;
}

.title-text {
  margin-left: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

@media (max-width: 1200px) {
  .page-header {
    flex-wrap: wrap;
    gap: 12px;
  }

  .header-actions {
    flex-wrap: wrap;
    gap: 8px;
  }
}

@media (max-width: 900px) {
  .title-text {
    white-space: normal;
    word-break: keep-all;
    overflow-wrap: break-word;
    line-height: 1.3;
  }
}

@media (max-width: 600px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
} */
</style>
