# Copyright (c) 2025 左岚. All rights reserved.

<template>
  <div class="base-card" :class="cardClasses">
    <!-- 卡片头部 -->
    <div class="card-header" v-if="$slots.header || showDefaultHeader">
      <slot name="header" :item="item">
        <div class="default-header">
          <h3 class="card-title">{{ getTitle() }}</h3>
          <div class="card-status" v-if="getStatus()">
            <el-tag :type="getStatusType()" size="small">
              {{ getStatus() }}
            </el-tag>
          </div>
        </div>
      </slot>
    </div>

    <!-- 卡片内容 -->
    <div class="card-content">
      <slot name="content" :item="item" :config="config">
        <div class="default-content">
          <p>请使用 content 插槽自定义卡片内容</p>
        </div>
      </slot>
    </div>

    <!-- 卡片底部操作区 -->
    <div class="card-footer" v-if="$slots.actions || showDefaultActions">
      <slot name="actions" :item="item">
        <div class="default-actions">
          <el-button size="small" @click="handleEdit($event)">编辑</el-button>
          <el-button size="small" type="primary" @click="handleTest($event)">测试</el-button>
          <el-button size="small" type="danger" @click="handleDelete($event)">删除</el-button>
        </div>
      </slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// 组件属性定义
interface Props {
  item: any; // 数据项
  config?: {
    titleField?: string; // 标题字段名
    statusField?: string; // 状态字段名
    size?: 'small' | 'medium' | 'large'; // 卡片尺寸
    shadow?: 'always' | 'hover' | 'never'; // 阴影效果
    style?: 'compact-info'; // 卡片样式风格（仅支持compact-info）
  };
  showDefaultHeader?: boolean; // 是否显示默认头部
  showDefaultActions?: boolean; // 是否显示默认操作按钮
}

// 组件事件定义
interface Emits {
  edit: [item: any, event: Event];
  test: [item: any, event: Event];
  delete: [item: any, event: Event];
}

const props = withDefaults(defineProps<Props>(), {
  config: () => ({}),
  showDefaultHeader: true,
  showDefaultActions: true
});

const emit = defineEmits<Emits>();

// 计算卡片样式类
const cardClasses = computed(() => {
  const classes = [];

  if (props.config?.size) {
    classes.push(`card-${props.config.size}`);
  }

  if (props.config?.shadow) {
    classes.push(`shadow-${props.config.shadow}`);
  }

  if (props.config?.style) {
    classes.push(`style-${props.config.style}`);
  }

  return classes;
});

// 获取标题
const getTitle = () => {
  if (props.config?.titleField && props.item) {
    return props.item[props.config.titleField] || '未命名';
  }
  return props.item?.name || props.item?.title || '未命名';
};

// 获取状态
const getStatus = () => {
  if (props.config?.statusField && props.item) {
    return props.item[props.config.statusField];
  }
  return props.item?.status || props.item?.state;
};

// 获取状态类型（用于标签颜色）
const getStatusType = () => {
  const status = getStatus();
  if (!status) return '';
  
  const statusStr = status.toString().toLowerCase();
  if (statusStr.includes('成功') || statusStr.includes('success') || statusStr.includes('active')) {
    return 'success';
  }
  if (statusStr.includes('失败') || statusStr.includes('error') || statusStr.includes('failed')) {
    return 'danger';
  }
  if (statusStr.includes('警告') || statusStr.includes('warning')) {
    return 'warning';
  }
  return 'info';
};

// 默认操作处理
const handleEdit = (event: Event) => emit('edit', props.item, event);
const handleTest = (event: Event) => emit('test', props.item, event);
const handleDelete = (event: Event) => emit('delete', props.item, event);
</script>

<style lang="scss">
.base-card {
  background: #fff;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
  overflow: hidden;

  // 卡片尺寸由内容自适应
  width: 100%;

  &:hover {
    border-color: #c0c4cc;
    transform: translateY(-2px);
  }
  
  // 尺寸变体 - 精细化调整
  &.card-small {
    .card-header { padding: 4px 12px 6px; } // 顶部padding减小一半：8px -> 4px，让数据源名称向上移动
    .card-content { padding: 0 12px 6px; } // 顶部padding设置为0
    .card-footer {
      padding: 8px 12px; // 固定操作栏高度：上下8px，左右12px
      height: 40px; // 强制固定高度
      box-sizing: border-box;
    }
  }

  &.card-medium {
    .card-header { padding: 12px 16px 8px; }
    .card-content { padding: 8px 16px; }
    .card-footer {
      padding: 8px 16px; // 固定操作栏高度：上下8px，左右16px
      height: 40px; // 强制固定高度
      box-sizing: border-box;
    }
  }

  &.card-large {
    .card-header { padding: 16px 20px 12px; }
    .card-content { padding: 12px 20px; }
    .card-footer {
      padding: 8px 20px; // 固定操作栏高度：上下8px，左右20px
      height: 40px; // 强制固定高度
      box-sizing: border-box;
    }
  }
  
  // 阴影变体
  &.shadow-always {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  // 卡片样式：紧凑信息风格
  
  &.shadow-hover {
    &:hover {
      box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
    }
  }

  &.shadow-never {
    box-shadow: none;
    &:hover {
      box-shadow: none;
    }
  }

  // 紧凑信息样式 - 唯一支持的卡片样式
  &.style-compact-info {
    background: #fafafa;
    border: 1px solid #e8e8e8;
    padding: 16px 16px 8px 16px; // 减小底部padding，让按钮能够下移

    .card-content {
      font-size: 14px;
      line-height: 1.4;
    }

    &:hover {
      background: #ffffff;
      border-color: #d9d9d9;
    }
  }


}

.card-header {
  padding: 16px 20px 12px;
  border-bottom: 1px solid #f0f0f0;
  
  .default-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .card-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }
}

.card-content {
  padding: 12px 20px;
  min-height: 60px;

  .default-content {
    color: #909399;
    text-align: center;
    font-style: italic;
  }
}

// 卡片操作栏统一样式 - 2025-01-31
// 确保所有页面的操作栏高度和样式一致，按钮右对齐
.card-footer {
  display: flex;
  justify-content: flex-end; // 强制右对齐
  align-items: center; // 垂直居中
  gap: 6px;
  padding: 8px 20px; // 统一内边距
  height: 40px; // 强制固定高度40px
  box-sizing: border-box; // 包含边框和内边距
  border-top: 1px solid #e4e7ed; // 分隔线，使用更明显的颜色
  background-color: transparent; // 操作栏背景色透明

  .el-button {
    font-size: 11px;
    padding: 3px 8px;
    height: 24px;
    border-radius: 4px;
    flex-shrink: 0; // 防止按钮被压缩
  }

  // 兼容旧的 card-actions 类名和默认操作 - 强制覆盖自定义样式
  .card-actions,
  .default-actions {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 6px;
    width: 100%;
    height: 100%; // 占满操作栏高度
    margin: 0 !important; // 强制清除所有margin，确保高度固定
    padding: 0 !important; // 强制清除所有padding，使用父容器的padding
    border: none !important; // 强制清除边框，使用父容器的边框
  }
}

/* 网格布局样式已移至 page-common.scss 中的卡片样式区域 */
</style>
