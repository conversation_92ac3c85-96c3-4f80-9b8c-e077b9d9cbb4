<template>
  <div class="visual-config-container">
    <!-- 调试信息 -->
    <div style="background: red; color: white; padding: 10px; margin-bottom: 10px;">
      🔧 可视化配置已加载 - 字段数量: {{ visualFields.length }}
    </div>

    <!-- 内容区域 - 使用flex布局 -->
    <div class="content-area">
      <!-- 顶部固定区域 -->
      <div class="header-section">
        <div class="config-tip">
          <el-icon><InfoFilled /></el-icon>
          配置哪些字段可以被搜索、过滤和排序，这将影响接口的查询能力
        </div>

        <!-- 数据源信息显示和操作按钮 -->
        <div class="datasource-info-header">
          <div class="datasource-info">
            <el-tag type="info" size="small">
              数据源：{{ getCurrentDatasourceName() }}
            </el-tag>
            <el-tag type="success" size="small" style="margin-left: 8px;">
              数据表：{{ formData?.tableName || formData?.table_name || '未选择' }}
            </el-tag>
          </div>
          <div class="datasource-actions">
            <el-button size="small" @click="handleLoadFromTable" :loading="loadingFromTable">
              <el-icon><Refresh /></el-icon>
              从数据表加载
            </el-button>
          </div>
        </div>
      </div>

      <!-- 字段配置表格 - 占据剩余空间 -->
      <div class="table-container">

      <div class="table-wrapper custom-scrollbar">
        <el-table
          :data="visualFields"
          border
          style="width: 100%; min-width: 1200px;"
          :height="tableHeight"
          :show-overflow-tooltip="true"
        >
        <el-table-column prop="name" label="字段名" width="120">
          <template #default="{ row }">
            <span>{{ row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="type" label="字段类型" width="120">
          <template #default="{ row }">
            <el-select
              v-if="row.source === 'custom'"
              v-model="row.type"
              size="small"
              style="width: 100%"
            >
              <el-option label="String(100)" value="String(100)" />
              <el-option label="Integer" value="Integer" />
              <el-option label="DateTime" value="DateTime" />
              <el-option label="Boolean" value="Boolean" />
              <el-option label="Text" value="Text" />
            </el-select>
            <span v-else>{{ row.type }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="comment" label="字段说明" min-width="150">
          <template #default="{ row }">
            <el-input
              v-model="row.comment"
              size="small"
              placeholder="字段说明"
            />
          </template>
        </el-table-column>



        <el-table-column prop="enabled" label="启用" width="60" align="center">
          <template #default="{ row }">
            <el-switch v-model="row.enabled" size="small" />
          </template>
        </el-table-column>

        <el-table-column prop="searchable" label="模糊搜索" width="80" align="center">
          <template #default="{ row }">
            <el-switch v-model="row.searchable" size="small" :disabled="!row.enabled" />
          </template>
        </el-table-column>

        <el-table-column prop="filterable" label="精确过滤" width="80" align="center">
          <template #default="{ row }">
            <el-switch v-model="row.filterable" size="small" :disabled="!row.enabled" />
          </template>
        </el-table-column>

        <el-table-column prop="rangeable" label="范围查询" width="80" align="center">
          <template #default="{ row }">
            <el-switch v-model="row.rangeable" size="small" :disabled="!row.enabled" />
          </template>
        </el-table-column>

        <el-table-column prop="sortable" label="排序" width="60" align="center">
          <template #default="{ row }">
            <el-switch v-model="row.sortable" size="small" :disabled="!row.enabled" />
          </template>
        </el-table-column>

        <el-table-column prop="sortOrder" label="排序方向" width="100" align="center">
          <template #default="{ row }">
            <el-select
              v-model="row.sortOrder"
              size="small"
              style="width: 80px"
              :disabled="!row.enabled || !row.sortable"
              placeholder="方向"
            >
              <el-option label="升序" value="asc" />
              <el-option label="降序" value="desc" />
            </el-select>
          </template>
        </el-table-column>


        </el-table>
      </div>
    </div>
    </div>

    <!-- 底部按钮已抽象到MainIndex中统一管理 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import { InfoFilled, Refresh, Plus, Search } from '@element-plus/icons-vue';
import { useGlobalDrawerStore } from '@/stores/globalDrawerStore.ts';
import { useGlobalDrawerMessenger } from '@/utils/piniaMessenger';
import dataSourceService from '@/services/datasource.service';


// 抽屉状态管理
const globalDrawerStore = useGlobalDrawerStore();
const drawerMessenger = useGlobalDrawerMessenger();

// 响应式数据
const visualFields = ref<any[]>([]);
const loadingFromTable = ref(false);
const dataSources = ref<any[]>([]);
const tableHeight = ref<number>(400);

// 计算属性 - 使用第二层抽屉的数据
const formData = computed(() => {
  const props = globalDrawerStore.secondProps;
  console.log('🔍 计算属性 formData - secondProps:', props);
  return props?.editData || props;
});

// 加载数据源列表
const loadDataSources = async () => {
  try {
    const response = await dataSourceService.getDataSources(1, 100); // 获取前100个数据源
    dataSources.value = response?.items || [];
  } catch (error) {
    console.error('加载数据源列表失败:', error);
    dataSources.value = [];
  }
};

// 获取数据源名称
const getCurrentDatasourceName = () => {
  const datasourceId = formData.value?.datasourceId || formData.value?.datasource_id;
  if (!datasourceId) {
    return '未选择数据源';
  }

  const dataSource = dataSources.value.find(ds => ds.id === datasourceId);
  return dataSource ? dataSource.name : `数据源ID: ${datasourceId}`;
};

// 从ORM配置和数据表结构加载字段
const loadFieldsFromOrmConfig = async () => {
  visualFields.value = [];

  console.log('🔍 loadFieldsFromOrmConfig 开始');
  console.log('🔍 globalDrawerStore.secondProps:', globalDrawerStore.secondProps);
  console.log('🔍 formData.value:', formData.value);

  if (!formData.value) {
    console.log('❌ formData.value 为空');
    return;
  }

  // 尝试多种可能的字段名
  const ormConfig = formData.value?.ormModelConfig || formData.value?.orm_model_config || formData.value?.orm_config;
  console.log('🔍 ormConfig:', ormConfig);
  console.log('🔍 ormConfig 类型:', typeof ormConfig);
  console.log('🔍 可用的ORM字段:', Object.keys(formData.value || {}).filter(k => k.toLowerCase().includes('orm')));

  // 第一步：从ORM配置中获取已配置的字段
  let ormFields = [];
  let queryMapping = null;

  // 第二步：从数据表结构获取完整字段列表
  let tableFields = [];
  try {
    if (formData.value.datasourceId && formData.value.tableName) {
      console.log('🔍 开始获取数据表结构');
      tableFields = await getTableStructure(formData.value.datasourceId, formData.value.tableName, formData.value.tableType);
      console.log('🔍 数据表字段：', tableFields);
    }
  } catch (error) {
    console.warn('⚠️ 获取数据表结构失败：', error);
  }

  // 第三步：解析ORM配置中的字段
  if (ormConfig) {
    try {
      const config = typeof ormConfig === 'string' ? JSON.parse(ormConfig) : ormConfig;
      console.log('🔍 解析后的ORM配置：', config);

      // 支持多种字段名格式（camelCase 和 snake_case）
      const sqlalchemyModel = config.sqlalchemyModel || config.sqlalchemy_model;
      queryMapping = config.queryMapping || config.query_mapping;

      if (sqlalchemyModel && sqlalchemyModel.fields) {
        ormFields = sqlalchemyModel.fields;
        console.log('🔍 ORM配置中的字段：', ormFields);
      }
    } catch (error) {
      console.warn('⚠️ ORM配置解析失败:', error);
    }
  }

  // 第四步：合并字段列表
  console.log('🔍 开始合并字段:', {
    ormFieldsCount: ormFields.length,
    tableFieldsCount: tableFields.length,
    ormFields: ormFields.map(f => f.name),
    tableFields: tableFields.map(f => f.name)
  });

  const mergedFields = mergeFieldsFromOrmAndTable(ormFields, tableFields, queryMapping);
  visualFields.value = mergedFields;

  console.log('✅ 最终合并的字段列表:', {
    totalCount: visualFields.value.length,
    enabledCount: visualFields.value.filter(f => f.enabled).length,
    fields: visualFields.value.map(f => ({ name: f.name, enabled: f.enabled, status: f.status }))
  });
};

// 获取数据表结构
const getTableStructure = async (datasourceId: number, tableName: string, tableType: string) => {
  try {
    console.log('🔍 调用表结构API:', { datasourceId, tableName, tableType });

    const result = await dataSourceService.getTableStructure(datasourceId, tableName, tableType);
    console.log('🔍 表结构API响应:', result);

    if (result && result.columns) {
      return result.columns;
    }
    return [];
  } catch (error) {
    console.error('❌ 获取表结构失败:', error);
    return [];
  }
};

// 合并ORM字段和数据表字段
const mergeFieldsFromOrmAndTable = (ormFields: any[], tableFields: any[], queryMapping: any) => {
  const fieldMap = new Map();

  // 第一步：添加数据表中的所有字段（默认禁用）
  tableFields.forEach(tableField => {
    fieldMap.set(tableField.name, {
      name: tableField.name,
      originalName: tableField.original_name || tableField.name,
      type: tableField.type,
      sqlalchemyType: tableField.sqlalchemy_type,
      comment: tableField.comment || '',
      nullable: tableField.nullable,
      primaryKey: tableField.primary_key || false,
      defaultValue: tableField.default,
      length: tableField.length,
      precision: tableField.precision,
      scale: tableField.scale,
      // 默认状态：从表结构来的字段默认禁用
      enabled: false,
      searchable: false,
      filterable: false,
      rangeable: false,
      sortable: false,
      // 不设置sortOrder，只有启用排序时才设置
      source: 'table_structure',
      status: 'from_table'
    });
  });

  // 第二步：覆盖ORM配置中的字段状态
  ormFields.forEach(ormField => {
    if (fieldMap.has(ormField.name)) {
      // 字段在表结构中存在，更新状态
      const existingField = fieldMap.get(ormField.name);
      const isSortable = queryMapping?.allowedSortFields?.includes(ormField.name) ||
                        queryMapping?.allowed_sort_fields?.includes(ormField.name) || false;

      const fieldConfig: any = {
        ...existingField,
        ...ormField,
        // 完全从查询映射中获取配置状态，不再依赖字段自身的配置（避免重复）
        searchable: queryMapping?.fuzzySearchFields?.includes(ormField.name) ||
                   queryMapping?.fuzzy_search_fields?.includes(ormField.name) || false,
        filterable: queryMapping?.exactMatchFields?.includes(ormField.name) ||
                   queryMapping?.exact_match_fields?.includes(ormField.name) || false,
        rangeable: queryMapping?.rangeQueryFields?.includes(ormField.name) ||
                  queryMapping?.range_query_fields?.includes(ormField.name) || false,
        sortable: isSortable,
        enabled: ormField.enabled !== undefined ? ormField.enabled : true,
        source: ormField.source || 'orm_config',
        status: 'in_orm'
      };

      // 只有在启用排序时才设置排序方向
      if (isSortable) {
        fieldConfig.sortOrder = ormField.sortOrder || ormField.sort_order ||
                               (ormField.name.toLowerCase().includes('id') ||
                                ormField.name.toLowerCase().includes('time') ||
                                ormField.name.toLowerCase().includes('date') ? 'desc' : 'asc');
      }

      fieldMap.set(ormField.name, fieldConfig);
    } else {
      // 字段在表结构中不存在（可能已删除），但保留在ORM中
      const isSortable = queryMapping?.allowedSortFields?.includes(ormField.name) ||
                        queryMapping?.allowed_sort_fields?.includes(ormField.name) || false;

      const fieldConfig: any = {
        ...ormField,
        // 完全从查询映射中获取配置状态，不再依赖字段自身的配置（避免重复）
        searchable: queryMapping?.fuzzySearchFields?.includes(ormField.name) ||
                   queryMapping?.fuzzy_search_fields?.includes(ormField.name) || false,
        filterable: queryMapping?.exactMatchFields?.includes(ormField.name) ||
                   queryMapping?.exact_match_fields?.includes(ormField.name) || false,
        rangeable: queryMapping?.rangeQueryFields?.includes(ormField.name) ||
                  queryMapping?.range_query_fields?.includes(ormField.name) || false,
        sortable: isSortable,
        enabled: ormField.enabled !== undefined ? ormField.enabled : true,
        source: ormField.source || 'orm_config',
        status: 'deleted_from_table' // 标记为已从表中删除
      };

      // 只有在启用排序时才设置排序方向
      if (isSortable) {
        fieldConfig.sortOrder = ormField.sortOrder || ormField.sort_order ||
                               (ormField.name.toLowerCase().includes('id') ||
                                ormField.name.toLowerCase().includes('time') ||
                                ormField.name.toLowerCase().includes('date') ? 'desc' : 'asc');
      }

      fieldMap.set(ormField.name, fieldConfig);
    }
  });

  return Array.from(fieldMap.values());
};

// 获取字段状态类型（用于标签颜色）
const getFieldStatusType = (status: string) => {
  switch (status) {
    case 'in_orm': return 'success';           // 绿色：在ORM中
    case 'from_table': return 'info';         // 蓝色：来自数据表
    case 'deleted_from_table': return 'warning'; // 橙色：已从表中删除
    case 'custom': return 'danger';           // 红色：自定义字段
    default: return 'info';
  }
};

// 获取字段状态文本
const getFieldStatusText = (status: string) => {
  switch (status) {
    case 'in_orm': return '已配置';
    case 'from_table': return '表字段';
    case 'deleted_from_table': return '已删除';
    case 'custom': return '自定义';
    default: return '未知';
  }
};

// 取消操作
const handleCancel = () => {
  drawerMessenger.hideDrawer(true);  // 关闭第二层抽屉
};

// 保存配置
const handleSave = () => {
  try {
    // 验证必填字段
    for (const field of visualFields.value) {
      if (field.enabled && !field.name.trim()) {
        ElMessage.error('请填写所有启用字段的名称');
        return;
      }
    }

    // 构建配置结果
    const enabledFields = visualFields.value.filter(field => field.enabled);
    const searchableFields = visualFields.value.filter(field => field.enabled && field.searchable).map(f => f.name);
    const filterableFields = visualFields.value.filter(field => field.enabled && field.filterable).map(f => f.name);
    const rangeableFields = visualFields.value.filter(field => field.enabled && field.rangeable).map(f => f.name);
    const sortableFields = visualFields.value.filter(field => field.enabled && field.sortable).map(f => f.name);

    // 构建排序字段配置，只包含启用排序的字段
    const sortFieldsConfig = {};
    visualFields.value
      .filter(field => field.enabled && field.sortable)
      .forEach(field => {
        if (field.sortOrder) {
          sortFieldsConfig[field.name] = field.sortOrder;
        }
      });

    // 分离配置：字段基本信息和查询映射配置
    console.log('🔍 [可视化配置保存] 所有字段：', visualFields.value);
    console.log('🔍 [可视化配置保存] 启用字段：', enabledFields);

    const fieldsConfig = {
      visualFields: enabledFields.map(field => {
        // 根据实际的ORM模型结构，传递字段必要的属性
        // 包含基本信息、数据库属性和查询相关属性
        return {
          name: field.name,
          originalName: field.originalName,      // 原始名称（必须）
          type: field.type,
          comment: field.comment,
          sqlalchemyType: field.sqlalchemyType,  // SQLAlchemy类型映射
          nullable: field.nullable,              // 是否可为空
          primaryKey: field.primaryKey,          // 是否为主键
          defaultValue: field.defaultValue,      // 默认值
          searchable: field.searchable,          // 是否可模糊搜索
          filterable: field.filterable,          // 是否可精确过滤
          rangeable: field.rangeable,            // 是否可范围查询
          sortable: field.sortable,              // 是否可排序
          // 只有启用了排序才添加排序方向
          ...(field.sortable && field.sortOrder && { sortOrder: field.sortOrder })
        };
      })
    };

    console.log('🔍 [可视化配置保存] 生成的字段配置：', fieldsConfig);

    const queryMappingConfig = {
      queryMapping: {
        fuzzy_search_fields: searchableFields,
        exact_match_fields: filterableFields,
        range_query_fields: rangeableFields,
        sort_fields_config: sortFieldsConfig  // 合并排序字段和方向配置
      }
    };

    console.log('🔍 保存字段配置：', fieldsConfig);
    console.log('🔍 保存查询映射配置：', queryMappingConfig);

    // 分别调用不同的回调函数处理两种配置
    // 1. 处理字段基本信息配置
    console.log('🔍 [回调调用] 准备调用字段配置回调，数据：', fieldsConfig);
    if ((window as any)?.handleFieldsConfigResult) {
      console.log('✅ [回调调用] 找到window.handleFieldsConfigResult，开始调用');
      (window as any).handleFieldsConfigResult(fieldsConfig);
    } else if ((window.parent as any)?.handleFieldsConfigResult) {
      console.log('✅ [回调调用] 找到window.parent.handleFieldsConfigResult，开始调用');
      (window.parent as any).handleFieldsConfigResult(fieldsConfig);
    } else {
      console.error('❌ [回调调用] 未找到字段配置回调函数 handleFieldsConfigResult');
      console.log('🔍 [回调调用] window对象：', window);
      console.log('🔍 [回调调用] window.parent对象：', window.parent);
    }

    // 2. 处理查询映射配置
    if ((window as any)?.handleQueryMappingResult) {
      (window as any).handleQueryMappingResult(queryMappingConfig);
    } else if ((window.parent as any)?.handleQueryMappingResult) {
      (window.parent as any).handleQueryMappingResult(queryMappingConfig);
    } else {
      console.warn('⚠️ 未找到查询映射回调函数 handleQueryMappingResult');
    }

    // 保持原有的统一回调，用于向后兼容
    const combinedResult = {
      ...fieldsConfig,
      ...queryMappingConfig
    };

    if ((window as any)?.handleVisualConfigResult) {
      (window as any).handleVisualConfigResult(combinedResult);
    } else if ((window.parent as any)?.handleVisualConfigResult) {
      (window.parent as any).handleVisualConfigResult(combinedResult);
    } else {
      console.error('❌ 未找到回调函数 handleVisualConfigResult');
    }

    ElMessage.success('配置保存成功');
    drawerMessenger.hideDrawer(true);  // 关闭第二层抽屉
  } catch (error) {
    ElMessage.error('保存配置失败，请检查数据格式');
  }
};

// 更新第二层抽屉底部按钮配置
const updateDrawerButtons = () => {
  console.log('🔧 InterfaceVisualConfigForm updateDrawerButtons 被调用');
  try {
    const rightButtons = [
      {
        text: '取消',
        handler: handleCancel
      },
      {
        text: '保存配置',
        type: 'primary' as const,
        handler: handleSave
      }
    ];

    // 更新第二层抽屉store中的按钮配置
    globalDrawerStore.secondLeftButtons = []; // 第二层抽屉没有左侧按钮
    globalDrawerStore.secondRightButtons = rightButtons;
    console.log('✅ 第二层抽屉按钮配置更新成功:', rightButtons);
  } catch (error) {
    console.error('❌ updateDrawerButtons 错误:', error);
  }
};

// 监听第二层抽屉属性变化
watch(() => globalDrawerStore.secondProps, async (newProps) => {
  console.log('可视化配置抽屉接收到props：', newProps);
  if (newProps && newProps.editData) {
    console.log('开始加载字段配置，editData：', newProps.editData);
    await loadFieldsFromOrmConfig();
  }
  updateDrawerButtons(); // 更新按钮配置
}, { immediate: true, deep: true });

// 从数据表加载字段
const handleLoadFromTable = async () => {
  const datasourceId = formData.value?.datasourceId || formData.value?.datasource_id;
  const tableName = formData.value?.tableName || formData.value?.table_name;
  const tableType = formData.value?.tableType || formData.value?.table_type || 'table';

  if (!datasourceId || !tableName) {
    ElMessage.warning('请先选择数据源和输入表名');
    return;
  }

  loadingFromTable.value = true;
  try {
    console.log('🔍 从数据表加载字段:', { datasourceId, tableName, tableType });

    // 调用真实的API获取表结构
    const tableFields = await getTableStructure(datasourceId, tableName, tableType);
    console.log('🔍 获取到的表字段:', tableFields);

    if (!tableFields || tableFields.length === 0) {
      ElMessage.warning('未获取到表字段信息');
      return;
    }

    // 将表字段转换为可视化配置字段
    visualFields.value = tableFields.map(field => ({
      name: field.name,
      originalName: field.original_name || field.name,
      type: field.type,
      sqlalchemyType: field.sqlalchemy_type,
      comment: field.comment || '',
      nullable: field.nullable,
      primaryKey: field.primary_key || false,
      defaultValue: field.default,
      length: field.length,
      precision: field.precision,
      scale: field.scale,
      // 默认配置：主键不可搜索，其他字段可搜索
      enabled: true,
      searchable: !field.primary_key,
      filterable: field.primary_key || field.name === 'status',
      rangeable: field.type?.toLowerCase().includes('date') || field.type?.toLowerCase().includes('time'),
      sortable: true,
      // 智能默认排序方向：ID和时间类字段默认降序，其他字段默认升序
      sortOrder: (field.primary_key ||
                 field.name.toLowerCase().includes('id') ||
                 field.name.toLowerCase().includes('time') ||
                 field.name.toLowerCase().includes('date') ||
                 field.name.toLowerCase().includes('created') ||
                 field.name.toLowerCase().includes('updated')) ? 'desc' : 'asc',
      source: 'table_structure',
      status: 'from_table'
    }));

    ElMessage.success(`成功加载 ${tableFields.length} 个字段`);
  } catch (error: any) {
    console.error('❌ 从数据表加载字段失败:', error);
    ElMessage.error(error.message || '加载字段失败');
  } finally {
    loadingFromTable.value = false;
  }
};





// 重复的函数定义已移至前面

// 计算表格高度
const calculateTableHeight = () => {
  // 获取视口高度
  const viewportHeight = window.innerHeight;
  // 抽屉的顶部偏移（通常是抽屉标题栏的高度）
  const drawerHeaderHeight = 60;
  // 顶部区域的高度（配置提示 + 数据源信息）
  const headerSectionHeight = 120;
  // 底部按钮区域的高度
  const footerHeight = 80;
  // 内边距和边距
  const padding = 40;

  // 计算表格可用高度
  const availableHeight = viewportHeight - drawerHeaderHeight - headerSectionHeight - footerHeight - padding;

  // 设置最小高度和最大高度
  const minHeight = 300;
  const maxHeight = 600;

  tableHeight.value = Math.max(minHeight, Math.min(maxHeight, availableHeight));

  console.log('🔍 表格高度计算:', {
    viewportHeight,
    availableHeight,
    finalHeight: tableHeight.value
  });
};

// 设置表头跟随表体滚动
const setupScrollSync = () => {
  // 等待DOM渲染完成
  setTimeout(() => {
    const headerWrapper = document.querySelector('.el-table__header-wrapper');
    const bodyWrapper = document.querySelector('.el-table__body-wrapper');

    if (headerWrapper && bodyWrapper) {
      // 只让表头跟随表体滚动（单向同步）
      bodyWrapper.addEventListener('scroll', () => {
        // 同步横向滚动位置
        if (headerWrapper.scrollLeft !== bodyWrapper.scrollLeft) {
          headerWrapper.scrollLeft = bodyWrapper.scrollLeft;
        }
      });

      console.log('✅ 表格滚动同步设置完成 - 表头跟随表体');
    } else {
      console.log('⚠️ 未找到表格滚动元素，稍后重试');
      // 如果元素还没有渲染，再次尝试
      setTimeout(setupScrollSync, 500);
    }
  }, 100);
};

onMounted(async () => {
  await loadDataSources(); // 加载数据源列表
  await loadFieldsFromOrmConfig();
  updateDrawerButtons(); // 初始化按钮配置

  // 计算表格高度
  calculateTableHeight();

  // 设置滚动同步
  setupScrollSync();

  // 监听窗口大小变化
  window.addEventListener('resize', calculateTableHeight);
});

// 组件卸载时清理事件监听器
onUnmounted(() => {
  window.removeEventListener('resize', calculateTableHeight);
});
</script>

<style lang="scss" scoped>
@use '@/assets/styles/page-common.scss' as *;

.visual-config-container {
  /* 抽屉容器布局 */
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #fff;

  .content-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    padding-bottom: 80px; /* 为DrawerFooter留出空间 */


    /* 应用项目标准滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: #9db7bd #f1f5f9;

    &::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #9db7bd;
      border-radius: 2px;
      transition: background 0.3s ease;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #7a9ca3;
    }

    &::-webkit-scrollbar-corner {
      background: #f1f5f9;
    }
  }

  .header-section {
    flex-shrink: 0; /* 顶部区域不收缩 */
    margin-bottom: 16px;
  }

  /* 2024-12-27: 滚动条样式已使用 page-common.scss 中的 .custom-scrollbar 公共样式 */
  /*
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: #9db7bd;
    border-radius: 2px;

    &:hover {
      background: #7a9ca3;
    }
  }

  scrollbar-width: thin;
  scrollbar-color: #9db7bd #f1f5f9;
  */

  /* 2024-12-27: 配置提示样式已抽象到 page-common.scss，使用公共样式 */
  /*
  .config-tip {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #f0f9ff;
    border: 1px solid #bfdbfe;
    border-radius: 6px;
    margin-bottom: 16px;
    font-size: 13px;
    color: #1e40af;
  }
  */

  .datasource-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;

    .datasource-info {
      display: flex;
      align-items: center;
    }

    .datasource-actions {
      display: flex;
      gap: 8px;
    }
  }

  .structure-change-alert {
    margin-bottom: 16px;

    .alert-actions {
      margin-top: 12px;
      display: flex;
      gap: 8px;
    }
  }

  .table-container {
    flex: 1; /* 占据剩余空间 */
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .custom-field-actions {
      display: flex;
      gap: 8px;
      margin-bottom: 16px;
      align-items: center;
      flex-shrink: 0;
    }

    .table-wrapper {
      flex: 1; /* 占据剩余空间 */
      border-radius: 4px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      position: relative;

      /* 鼠标悬停时只显示表格内容区域的滚动条 */
      &:hover {
        :deep(.el-table) {
          .el-table__body-wrapper {
            scrollbar-color: #9db7bd #f1f5f9;

            &::-webkit-scrollbar-track {
              background: #f1f5f9;
            }

            &::-webkit-scrollbar-thumb {
              background: #9db7bd;
            }

            &::-webkit-scrollbar-corner {
              background: #f1f5f9;
            }
          }

          /* 表头滚动条始终隐藏 */
          .el-table__header-wrapper {
            &::-webkit-scrollbar {
              display: none !important;
            }
          }
        }
      }

      /* 应用自定义滚动条样式 */
      :deep(.el-table) {
        height: 100%;

        /* 强制启用表格内容区域的滚动 */
        .el-table__body-wrapper {
          overflow: auto !important; /* 强制启用滚动 */

          /* 确保横向滚动条显示 */
          overflow-x: auto !important;
          overflow-y: auto !important;

          /* 默认隐藏滚动条，鼠标悬停时显示 */
          scrollbar-width: thin;
          scrollbar-color: transparent transparent;

          &::-webkit-scrollbar {
            width: 4px;
            height: 4px;
            display: block !important; /* 强制显示滚动条 */
          }

          &::-webkit-scrollbar-track {
            background: transparent;
            border-radius: 2px;
          }

          &::-webkit-scrollbar-thumb {
            background: transparent;
            border-radius: 2px;
            transition: background 0.3s ease;
          }

          &::-webkit-scrollbar-corner {
            background: transparent;
          }
        }

        /* 表头跟随内容滚动，但不显示滚动条 */
        .el-table__header-wrapper {
          overflow-x: hidden !important; /* 隐藏表头的横向滚动条 */
          overflow-y: hidden !important;

          /* 完全隐藏表头滚动条 */
          scrollbar-width: none;

          &::-webkit-scrollbar {
            display: none !important; /* 完全隐藏表头滚动条 */
          }

          /* 确保表头固定 */
          position: sticky;
          top: 0;
          z-index: 10;
          background: #fff;
        }

        /* 完全隐藏Element Plus的虚拟滚动条 */
        .el-scrollbar__bar {
          display: none !important;
          opacity: 0 !important;
        }

        .el-scrollbar__wrap {
          overflow: visible !important;
        }

        /* 确保滚动区域可见 */
        .el-scrollbar {
          overflow: visible !important;
        }

        /* 强制显示表格内容区域的滚动条 */
        .el-table__body {
          overflow: visible !important;
        }

        /* 确保表格内容可以滚动 */
        &.el-table--scrollable-x .el-table__body-wrapper {
          overflow-x: auto !important;
        }

        &.el-table--scrollable-y .el-table__body-wrapper {
          overflow-y: auto !important;
        }

        /* 强制表格内容区域显示滚动条 */
        .el-table__body-wrapper {
          /* 确保滚动条容器存在 */
          position: relative !important;

          /* 强制启用滚动 */
          overflow: auto !important;

          /* 确保横向滚动条显示 */
          min-height: 100px; /* 确保有足够高度显示横向滚动条 */
        }
      }

      /* 额外的滚动条强制显示样式 */
      :deep(.el-table) {
        /* 确保表格可以滚动 */
        overflow: visible !important;

        /* 强制启用表格内容区域滚动条 */
        .el-table__body-wrapper {
          scrollbar-width: thin !important;

          /* 强制显示滚动条 */
          &::-webkit-scrollbar {
            display: block !important;
            -webkit-appearance: none !important;
          }

          /* 确保滚动条在正确位置 */
          &::-webkit-scrollbar:horizontal {
            height: 4px !important;
          }

          &::-webkit-scrollbar:vertical {
            width: 4px !important;
          }
        }

        /* 确保表头滚动条隐藏 */
        .el-table__header-wrapper {
          &::-webkit-scrollbar {
            display: none !important;
          }
        }
      }
    }

    /* 2024-12-27: 表格滚动条样式已使用 page-common.scss 中的 .custom-scrollbar 公共样式 */
    /*
    &::-webkit-scrollbar {
      width: 4px;
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #9db7bd;
      border-radius: 2px;

      &:hover {
        background: #7a9ca3;
      }
    }

    scrollbar-width: thin;
    scrollbar-color: #9db7bd #f1f5f9;
    */
  }
}


/* DrawerFooter固定在底部的样式已在page-common.scss中定义 */
</style>
