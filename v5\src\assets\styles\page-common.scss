/* 通用页面样式 - 基于数据源列表页面提取 */

/* 最高优先级覆盖Element Plus抽屉头部padding - 2024-12-27 */
.el-drawer__header,
.el-drawer .el-drawer__header,
div.el-drawer__header,
[class*="el-drawer"] .el-drawer__header {
  padding: 0 !important;
  padding-top: 0 !important;
  padding-bottom: 0 !important;
  padding-left: 0 !important;
  padding-right: 0 !important;
}

/*
 * 滚动条样式规范 - 2024-12-27
 * ========================================
 * 所有页面的竖向滚动条都应该使用统一的样式规范：
 *
 * 1. 宽度：4px（细滚动条）
 * 2. 轨道颜色：#f1f5f9（浅灰色）
 * 3. 滑块颜色：#9db7bd（蓝灰色）
 * 4. 悬停颜色：#7a9ca3（深蓝灰色）
 * 5. 圆角：2px
 * 6. Firefox支持：scrollbar-width: thin; scrollbar-color: #9db7bd #f1f5f9;
 *
 * 使用方式：
 * - .container 类自动应用滚动条样式
 * - 其他元素可以使用 .custom-scrollbar 类
 * - 或直接复制相关CSS规则到组件中
 *
 * 禁止在组件中自定义不同的滚动条样式！
 */

/* 页面容器样式 - 2024-12-26: 统一容器样式，保留原有滚动条设置 */
/* 2024-12-27: 修改margin为自动计算，减少底部空白 */
.container {
  max-width: calc(100% - 20px);
  margin: 10px 10px 5px 10px; /* 顶部10px，左右10px，底部5px */
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  padding: 20px;
  max-height: calc(100vh - 25px); /* 相应调整最大高度 */
  overflow-y: auto;
  overflow-x: hidden;
  /* Firefox滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #9db7bd #f1f5f9;
}

/* 页面头部样式 - 2024-12-26: 统一页面头部样式 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid #ebeef5;
}

.page-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 500;
  color: #303133;
  flex-wrap: wrap;
  min-width: 0;
}

.page-title .el-icon {
  margin-right: 8px;
  font-size: 20px;
  color: #3FC8DD;
}

.title-text {
  margin-left: 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-input {
  width: 250px;
}

/* 页签样式 - 2024-12-26: 统一页签样式 */
.page-tabs {
  margin-top: 0;
}

:deep(.el-tabs__header) {
  margin-bottom: 15px;
}

:deep(.el-tabs__nav-wrap::after) {
  display: none;
}

:deep(.el-tabs__active-bar) {
  background-color: #3FC8DD;
}

:deep(.el-tabs__item.is-active) {
  color: #3FC8DD;
}

:deep(.el-tabs__item:hover) {
  color: #3FC8DD;
}

.page-tabs :deep(.el-tabs__content) {
  padding: 0 !important; /* 移除所有内边距 */
}

/* 表格基础样式 - 2024-12-26: 统一表格样式 */
:deep(.el-table) {
  border: none;
  width: max-content !important; /* 让表格根据内容自动计算宽度 */
  min-width: 100% !important; /* 最小宽度为容器宽度 */
  table-layout: fixed !important; /* 使用固定布局，尊重列宽设置 */
  overflow-y: hidden !important;
}

:deep(.el-table__header) {
  background: #fafafa;
  width: 100% !important;
  min-width: fit-content !important;
}

:deep(.el-table__header th) {
  background: transparent;
  color: #303133;
  font-weight: bold;
  border-bottom: 1px solid #ebeef5;
  border-left: none;
  border-right: none;
  border-top: none;
  padding: 12px 0;
}

:deep(.el-table__body) {
  width: 100% !important;
  min-width: fit-content !important;
}

:deep(.el-table__body tr:hover) {
  background-color: #f5f7fa;
}

:deep(.el-table__body td) {
  padding: 12px 0;
  border-bottom: 1px solid #ebeef5;
  border-left: none;
  border-right: none;
  border-top: none;
}

/* 表格滚动条视图修复 */
:deep(.el-scrollbar__view) {
  display: block !important;
  width: 100% !important;
}

/* 表格滚动条样式 - 2024-12-26: 统一表格滚动条样式 */
:deep(.el-table__header-wrapper) {
  overflow-y: hidden !important;
  overflow-x: auto !important;
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

:deep(.el-table__header-wrapper):hover {
  scrollbar-color: #9db7bd transparent;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar) {
  height: 6px;
  width: 0px !important;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.el-table__header-wrapper::-webkit-scrollbar-thumb) {
  background: transparent;
  border-radius: 3px;
  transition: background 0.3s;
}

:deep(.el-table__header-wrapper):hover::-webkit-scrollbar-thumb {
  background: #9db7bd;
}

:deep(.el-table__body-wrapper) {
  overflow-y: hidden !important; /* 隐藏竖向滚动 */
  overflow-x: auto !important;   /* 显示横向滚动 */
  scrollbar-width: thin;
  scrollbar-color: transparent transparent;
}

:deep(.el-table__body-wrapper):hover {
  overflow-y: hidden !important;
  scrollbar-color: #9db7bd transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 6px;
  width: 0px !important;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar:horizontal) {
  height: 6px; /* 显示横向滚动条 */
}

:deep(.el-table__body-wrapper::-webkit-scrollbar:vertical) {
  display: none !important;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: transparent;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: transparent;
  border-radius: 3px;
  transition: background 0.3s;
}

:deep(.el-table__body-wrapper):hover::-webkit-scrollbar-thumb {
  background: #9db7bd;
}

/* 隐藏Element Plus内部滚动条组件 */
:deep(.el-scrollbar__bar.is-vertical) {
  display: none !important;
}

:deep(.el-table .el-scrollbar__bar.is-vertical) {
  display: none !important;
}

:deep(.el-table:hover .el-scrollbar__bar.is-vertical) {
  display: none !important;
}

/* 操作按钮样式 - Element标准 */
:deep(.el-button--small) {
  padding: 5px 11px;
  font-size: 12px;
  border-radius: 3px;
}

/* 默认按钮 */
:deep(.el-button.el-button--small:not([class*="el-button--"])) {
  color: #606266;
  border-color: #dcdfe6;
  background-color: #fff;
}

:deep(.el-button.el-button--small:not([class*="el-button--"]):hover) {
  color: #409eff;
  border-color: #c6e2ff;
  background-color: #ecf5ff;
}

/* 主要按钮 */
:deep(.el-button--primary) {
  color: #fff;
  background-color: #3FC8DD;
  border-color: #3FC8DD;
}

:deep(.el-button--primary:hover) {
  background-color: #35b3c7;
  border-color: #35b3c7;
}

/* 小尺寸主要按钮 */
:deep(.el-button--primary.el-button--small) {
  color: #fff;
  background-color: #3FC8DD;
  border-color: #3FC8DD;
}

:deep(.el-button--primary.el-button--small:hover) {
  background-color: #35b3c7;
  border-color: #35b3c7;
}

/* 成功按钮 */
:deep(.el-button--success.el-button--small) {
  color: #fff;
  background-color: #67c23a;
  border-color: #67c23a;
}

:deep(.el-button--success.el-button--small:hover) {
  background-color: #85ce61;
  border-color: #85ce61;
}

/* 危险按钮 */
:deep(.el-button--danger.el-button--small) {
  color: #fff;
  background-color: #f56c6c;
  border-color: #f56c6c;
}

:deep(.el-button--danger.el-button--small:hover) {
  background-color: #f78989;
  border-color: #f78989;
}

/* 测试按钮 - 2024-12-26: 统一测试按钮灰色样式 */
:deep(.el-button--info.el-button--small) {
  color: #fff;
  background-color: #909399;
  border-color: #909399;
}

:deep(.el-button--info.el-button--small:hover) {
  background-color: #a6a9ad;
  border-color: #a6a9ad;
}

/* 测试按钮（普通尺寸） */
:deep(.el-button--info) {
  color: #fff;
  background-color: #909399;
  border-color: #909399;
}

:deep(.el-button--info:hover) {
  background-color: #a6a9ad;
  border-color: #a6a9ad;
}

/* 状态文字样式 - 2024-12-26: 统一状态显示样式 */
.status-enabled {
  color: #67c23a;
  font-weight: 500;
}

.status-disabled {
  color: #f56c6c;
  font-weight: 500;
}

/* 表单提示样式 - 2024-12-26: 统一表单提示样式 */
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

/* 分页样式 */
.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

/* 对话框样式 - Element标准 */
/* 2024-12-27: 修复对话框在iframe中的居中问题 */
:deep(.el-overlay-dialog) {
  top: auto !important;  /*去掉top=0的限制 */
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding-bottom: 20px !important; /* 向上偏移20px */
}

:deep(.el-dialog) {
  margin: 0 !important;
  padding: 0 !important;
  position: relative !important;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__title) {
  font-weight: 500;
  color: #303133;
  font-size: 18px;
}

:deep(.el-dialog__body) {
  padding: 30px 20px;
  color: #606266;
  font-size: 14px;
}

/* 对话框底部样式 - 2024-12-26: 统一对话框底部按钮布局 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 抽屉通用样式 - 2024-12-27: 从各抽屉实例中抽象的公共样式 */

/* 抽屉表单样式 */
.drawer-form {
  :deep(.el-form-item) {
    margin-bottom: 16px;
  }
}

/* 抽屉下拉选择样式 */
.drawer-select {
  :deep(.el-select) {
    width: 100%;

    .el-input {
      width: 100%;
    }

    .el-input__wrapper {
      width: 100%;
    }
  }

  :deep(.el-select-dropdown) {
    z-index: 20000 !important;
  }

  :deep(.el-popper) {
    z-index: 20000 !important;
  }

  :deep(.el-select-dropdown__wrap) {
    z-index: 20000 !important;
  }

  :deep(.el-scrollbar) {
    z-index: 20000 !important;
  }

  :deep(.el-overlay) {
    z-index: 19999 !important;
  }

  :deep(.el-select .el-input.is-focus .el-input__wrapper) {
    box-shadow: 0 0 0 1px #409eff inset;
  }

  :deep(.high-z-index-popper) {
    z-index: 25000 !important;
  }
}

/* 测试结果样式不抽象，各抽屉保持独立 */

/* 内层滚动条样式 */
.container::-webkit-scrollbar {
  width: 4px;
}

.container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.container::-webkit-scrollbar-thumb {
  background: #9db7bd;
  border-radius: 2px;
  transition: background 0.3s ease;
}

.container::-webkit-scrollbar-thumb:hover {
  background: #7a9ca3;
}

/* Firefox滚动条样式已合并到容器样式中 - 2024-12-26 */

/* 通用滚动条样式类 - 2024-12-27: 统一滚动条规范 */
/* 任何需要滚动条的元素都可以使用这个类或直接应用这些样式 */
.custom-scrollbar,
:deep(.custom-scrollbar) {
  /* Firefox滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #9db7bd #f1f5f9;
}

.custom-scrollbar::-webkit-scrollbar,
:deep(.custom-scrollbar)::-webkit-scrollbar {
  width: 4px;
}

.custom-scrollbar::-webkit-scrollbar-track,
:deep(.custom-scrollbar)::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 2px;
}

.custom-scrollbar::-webkit-scrollbar-thumb,
:deep(.custom-scrollbar)::-webkit-scrollbar-thumb {
  background: #9db7bd;
  border-radius: 2px;
  transition: background 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover,
:deep(.custom-scrollbar)::-webkit-scrollbar-thumb:hover {
  background: #7a9ca3;
}

/* 响应式设计 - 2024-12-26: 统一响应式样式 */
@media (max-width: 1200px) {
  .page-header {
    flex-wrap: wrap;
    gap: 12px;
  }

  .header-actions {
    flex-wrap: wrap;
    gap: 8px;
  }
}

@media (max-width: 900px) {
  .title-text {
    white-space: normal;
    word-break: keep-all;
    overflow-wrap: break-word;
    line-height: 1.3;
  }
}

@media (max-width: 768px) {
  .container {
    margin: 3px 3px 2px 3px; /* 移动端也减少底部margin */
    padding: 12px;
  }

  .search-input {
    width: 100%;
  }
}

@media (max-width: 600px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
}

/* ========================================
 * 右侧抽屉弹出页面样式 - 2024-12-27
 * ========================================
 * 统一的右侧抽屉样式，用于新增/编辑等操作
 * 特点：
 * 1. 从右侧滑入，占据35%屏幕宽度
 * 2. 覆盖整个页面（包括头部和页签）
 * 3. 关闭按钮在左上角
 * 4. 统一的滚动条样式
 * 5. 响应式设计
 * 6. 通过全局抽屉store管理，在框架层级生成
 *
 * 实现方式：
 * 1. 在MainIndex.vue中添加全局抽屉组件
 * 2. 通过useGlobalDrawerStore管理抽屉状态
 * 3. 抽屉内容通过动态组件加载
 * 4. iframe页面通过store控制抽屉显示/隐藏
 */

.common-drawer {

  /* 抽屉内容区域 */
  .drawer-content {
    padding: 0 20px;
    height: calc(100vh - 140px);
    overflow-y: auto;

    /* 应用统一滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: #9db7bd #f1f5f9;

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f5f9;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #9db7bd;
      border-radius: 2px;
      transition: background 0.3s ease;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #7a9ca3;
    }
  }

  /* 抽屉底部按钮区域 */
  .drawer-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 16px 20px;
    border-top: 1px solid #e4e7ed;
    background: #fff;
    position: sticky;
    bottom: 0;
    z-index: 10;
  }

  /* 表单样式优化 */
  .el-form {
    .el-form-item {
      margin-bottom: 24px;
    }

    .el-input {
      width: 100%;
    }

    .el-select {
      width: 100%;
    }

    .el-input-number {
      width: 100%;
    }

    .el-textarea {
      width: 100%;
    }
  }
}

/* 抽屉组件本身的样式覆盖 */
:deep(.el-drawer) {
  /* 确保抽屉覆盖整个页面，包括框架头部 */
  z-index: 9999 !important;

  /* 优化动画性能 */
  will-change: transform;
  transform: translateZ(0);

  /* 使用更流畅的动画曲线 */
  transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
}

:deep(.el-drawer__body) {
  padding: 0;
  overflow: hidden;

  /* 优化渲染性能 */
  will-change: transform;
  transform: translateZ(0);

  /* 确保内容平滑滚动 */
  -webkit-overflow-scrolling: touch;
}

/* 抽屉标题样式 - 使用更强的选择器确保覆盖Element Plus默认样式 */
.el-drawer .el-drawer__header,
:deep(.el-drawer .el-drawer__header),
:deep(.el-drawer__header) {
  padding: 0 !important;
  border-bottom: none !important;
  background: #fff !important;
  margin-bottom: 0 !important; /* 强制去掉Element Plus默认的32px/36px底部间距 */
  margin: 0 !important; /* 确保所有margin都被重置 */
}

:deep(.el-drawer__title) {
  font-weight: 500;
  color: #303133;
  font-size: 18px;
}

:deep(.el-drawer__close-btn) {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  right: auto;
  font-size: 16px;
  color: #909399;

  &:hover {
    color: #606266;
  }
}

:deep(.el-drawer__footer) {
  padding: 0;
}

/* 优化遮罩层动画 */
:deep(.el-overlay) {
  transition: opacity 0.3s ease !important;
  backdrop-filter: blur(2px);
  -webkit-backdrop-filter: blur(2px);
}

/* 优化抽屉容器动画 */
:deep(.el-drawer__container) {
  transition: transform 0.3s cubic-bezier(0.25, 0.8, 0.25, 1) !important;
  will-change: transform;
  transform: translateZ(0);
}

/* 强制覆盖抽屉内部的滚动条样式 */
:deep(.el-drawer .el-scrollbar__wrap) {
  scrollbar-width: thin !important;
  scrollbar-color: #9db7bd #f1f5f9 !important;
}

:deep(.el-drawer .el-scrollbar__wrap::-webkit-scrollbar) {
  width: 4px !important;
}

:deep(.el-drawer .el-scrollbar__wrap::-webkit-scrollbar-track) {
  background: #f1f5f9 !important;
  border-radius: 2px !important;
}

:deep(.el-drawer .el-scrollbar__wrap::-webkit-scrollbar-thumb) {
  background: #9db7bd !important;
  border-radius: 2px !important;
  transition: background 0.3s ease !important;
}

:deep(.el-drawer .el-scrollbar__wrap::-webkit-scrollbar-thumb:hover) {
  background: #7a9ca3 !important;
}

/* 覆盖Element Plus抽屉内部滚动条 */
:deep(.el-drawer__body) {
  scrollbar-width: thin !important;
  scrollbar-color: #9db7bd #f1f5f9 !important;
}

:deep(.el-drawer__body::-webkit-scrollbar) {
  width: 4px !important;
}

:deep(.el-drawer__body::-webkit-scrollbar-track) {
  background: #f1f5f9 !important;
  border-radius: 2px !important;
}

:deep(.el-drawer__body::-webkit-scrollbar-thumb) {
  background: #9db7bd !important;
  border-radius: 2px !important;
  transition: background 0.3s ease !important;
}

:deep(.el-drawer__body::-webkit-scrollbar-thumb:hover) {
  background: #7a9ca3 !important;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .common-drawer {
    /* 在较小屏幕上增加抽屉宽度 */
    .el-drawer {
      width: 45% !important;
    }
  }
}

@media (max-width: 768px) {
  .common-drawer {
    /* 在移动端占据更多空间 */
    .el-drawer {
      width: 80% !important;
    }

    .drawer-content {
      padding: 0 16px;
    }

    .drawer-footer {
      padding: 12px 16px;
    }

    /* 在小屏幕上减少标签宽度 */
    .el-form {
      --el-form-label-width: 80px;
    }
  }
}

@media (max-width: 480px) {
  .common-drawer {
    /* 在小屏手机上几乎全屏 */
    .el-drawer {
      width: 95% !important;
    }

    /* 在超小屏幕上进一步减少标签宽度 */
    .el-form {
      --el-form-label-width: 60px;
    }
  }
}

/* ========================================
 * 抽屉底部按钮区域样式 - 2024-12-27
 * ========================================
 * 统一的抽屉底部按钮样式，用于所有抽屉组件
 * 特点：
 * 1. 固定在抽屉底部
 * 2. 带有分割线
 * 3. 支持左右布局（左侧功能按钮，右侧确认/取消按钮）
 * 4. 统一的间距和样式
 * 5. 阴影效果增强层次感
 *
 * 使用方式：
 * 1. 表单内容区域需要设置 padding-bottom: 80px 为底部按钮留出空间
 * 2. 使用 .drawer-footer-fixed 作为底部按钮容器
 * 3. 使用 .drawer-divider 添加分割线
 * 4. 使用 .drawer-footer 包装按钮
 * 5. 使用 .drawer-footer-right 右对齐按钮组
 */

/* 抽屉底部按钮固定容器 - 2024-12-27: 改回昨天的通长样式 */
.drawer-footer-fixed {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: #fff;
  z-index: 10001;
  border-top: 1px solid #e4e7ed;
  /* 移除box-shadow，使用border-top分割线 */
}

/* 抽屉底部分割线 - 2024-12-27: 不再需要单独的分割线 */
.drawer-divider {
  display: none;
  /* 使用border-top替代 */
}

/* 抽屉底部按钮容器 - 2024-12-27: 使用负margin向上移动 */
.drawer-footer {
  padding: 15px 20px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
  height: 60px;
  box-sizing: border-box;
  margin: 0;
  margin-bottom: -20px;

  .el-button {
    margin-left: 0;
    /* 使用gap替代margin */
  }
}

/* 抽屉底部左右按钮组 - 2024-12-27: 适配通长底部样式 */
.drawer-footer-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.drawer-footer-right {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: auto;

  .el-button {
    margin-left: 0;
    /* 使用gap替代margin */
  }
}

/* 抽屉表单内容区域 - 为底部按钮留出空间 */
.drawer-form-content {
  height: 100%;
  overflow: auto;
  padding: 0 20px;
  padding-bottom: 70px;

  /* 应用统一滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: #9db7bd #f1f5f9;

  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: #9db7bd;
    border-radius: 2px;
    transition: background 0.3s ease;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: #7a9ca3;
  }
}

/* ========================================
 * 抽屉通用颜色样式 - 2024-12-27
 * ========================================
 * 从各抽屉组件中抽象的共性颜色样式
 * 用于：需要显示颜色值的抽屉组件
 */

/* 颜色值显示样式 - 通用样式，多个组件都有显示颜色值的需求 */
.color-value {
  font-family: monospace;
  font-size: 14px;
  color: #606266;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
}

/* ========================================
 * 抽屉通用反馈样式 - 2024-12-27
 * ========================================
 * 从接口配置管理抽屉中抽象的共性反馈样式
 * 用于：验证结果、操作反馈、状态提示等
 */

/* 验证结果反馈样式 - 通用样式，多个抽屉都有验证反馈需求 */
.validation-result {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 14px;

  &.success {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    color: #0369a1;

    .el-icon {
      color: #10b981;
    }
  }

  &.error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;

    .el-icon {
      color: #ef4444;
    }
  }

  &.warning {
    background: #fffbeb;
    border: 1px solid #fed7aa;
    color: #92400e;

    .el-icon {
      color: #f59e0b;
    }
  }
}

/* 表单提示文字样式已在第361行定义，使用统一的#909399颜色 */

/* ========================================
 * 抽屉通用反馈样式 - 2024-12-27
 * ========================================
 * 从接口配置管理抽屉中抽象的共性反馈样式
 * 用于：验证结果、操作反馈、状态提示等
 */

/* 验证结果反馈样式 - 通用样式，多个抽屉都有验证反馈需求 */
.validation-result {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 6px;
  font-size: 14px;

  &.success {
    background: #f0f9ff;
    border: 1px solid #bae6fd;
    color: #0369a1;

    .el-icon {
      color: #10b981;
    }
  }

  &.error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;

    .el-icon {
      color: #ef4444;
    }
  }

  &.warning {
    background: #fffbeb;
    border: 1px solid #fed7aa;
    color: #92400e;

    .el-icon {
      color: #f59e0b;
    }
  }
}

/* 表单提示文字样式 - 通用样式，所有表单都需要提示文字 */
.form-tip {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
  line-height: 1.4;
}

/* ========================================
 * 抽屉配置提示样式 - 2024-12-27
 * ========================================
 * 从接口可视化配置抽屉中抽象的配置提示样式
 * 用于：操作指引、注意事项、状态提示等
 */

/* 配置提示框样式 - 通用样式，多个抽屉都有信息提示需求 */
.config-tip {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 6px;
  margin-bottom: 16px;
  font-size: 13px;
  color: #1e40af;
}

/* ========================================
 * 卡片网格布局样式 - 2025-01-31
 * ========================================
 * 用于父组件的卡片网格布局样式
 * 应用场景：数据源管理、接口分组管理等卡片视图页面
 *
 * 使用方式：
 * 在包含BaseCard组件的父容器上添加 .base-card-grid 类
 *
 * 布局标准：
 * - 卡片视图：每页15条记录，严格3行×5列布局
 * - 列表视图：每页10条记录，表格形式
 *
 * 响应式断点：
 * - 手机端(≤768px)：1列×15行
 * - 平板端(769px-1024px)：3列×5行
 * - 桌面端(≥1025px)：固定5列×3行
 *
 * 注意事项：
 * 1. 此样式用于父容器，不属于BaseCard组件内部
 * 2. 后续新增的卡片相关样式也应添加到此区域
 * 3. 保持与分页标准(15条卡片)的一致性
 */

.base-card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr)); // 减小最小宽度，便于每行显示更多卡片
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px; // 增加内边距，让卡片与容器边缘有间距

  // 卡片容器背景色设置 - 增强视觉层次感
  background: #f5f7fa; // 稍微浅一点的灰色背景，平衡视觉效果
  border-radius: 8px; // 圆角边框

  // 手机端：1列
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  // 平板端：2-3列
  @media (min-width: 769px) and (max-width: 1024px) {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 14px;
  }

  // 桌面端：3-4列
  @media (min-width: 1025px) and (max-width: 1400px) {
    grid-template-columns: repeat(auto-fill, minmax(260px, 1fr));
    gap: 16px;
  }

  // 大屏幕：理想的5列布局
  @media (min-width: 1401px) {
    grid-template-columns: repeat(5, 1fr); // 固定5列，自动适配宽度
    gap: 18px;
  }

  // 超大屏幕：保持5列但增加间距
  @media (min-width: 1800px) {
    grid-template-columns: repeat(5, 1fr);
    gap: 20px;
    max-width: 1600px; // 限制最大宽度，避免卡片过宽
    margin: 0 auto 24px;
  }
}



/*
 * 视图切换图标标准样式 - 2025-01-31
 * ========================================
 * 统一所有页面的视图切换图标大小，确保一致性
 * 参考数据源页面的标准实现
 */

.view-toggle .el-button-group .el-button .el-icon {
  font-size: 18px !important; // 统一图标大小为18px，与数据源页面保持一致
}

/*
 * 背景过渡方案测试 - 2025-01-31
 * ========================================
 * 方案A：渐进式背景过渡（数据源页面）
 * 方案B：统一背景策略（接口分组页面）
 */

// 统一背景方案：使用稍深的背景色 #f5f5f5
.page-container-gradient,
.page-container-unified {
  background: #ffffff; // 页面主容器：纯白色外圈
  min-height: 100vh;

  // 卡片视图容器统一背景色
  .base-card-grid {
    background: #f5f5f5; // 卡片容器：稍深的背景色 #f5f5f5
  }

  // 列表视图保持原样，不受影响
  .table-container,
  .list-view {
    background: inherit; // 继承父容器背景，保持原样
  }
}

/* ========================================
 * 卡片内容标准化样式规则
 * ========================================
 * 用于统一所有卡片的内容结构和样式
 *
 * 规则1：卡片内容结构标准化
 * - 容器类名：统一使用 card-content（不使用业务前缀）
 * - 头部类名：统一使用 card-header
 * - 信息区域：统一使用 main-info
 * - 底部栏：统一使用 status-bar
 *
 * 规则2：标题样式标准化
 * - 标题类名：统一使用 card-title（不使用业务前缀）
 * - 标题对齐：左对齐
 * - 状态标签：右对齐，使用 status-tag 类名
 *
 * 规则3：卡片尺寸标准化
 * - 统一使用：BaseCard的 size="small" 配置
 * - 统一样式：style="compact-info"
 * - 删除自定义样式：移除所有影响尺寸的自定义CSS
 */

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .card-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #303133;
    text-align: left; // 标题左对齐
  }

  .status-tag {
    margin-left: auto; // 状态标签右对齐
  }
}

.main-info {
  margin-bottom: 16px;

  .info-row {
    display: flex;
    gap: 16px;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .info-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #606266;

    .info-icon {
      margin-right: 8px;
      color: #909399;
      font-size: 16px;
    }

    .info-text {
      color: #606266;
    }
  }
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;

  .update-time {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #909399;

    .time-icon {
      margin-right: 4px;
      font-size: 14px;
    }

    .time-text {
      color: #909399;
    }
  }
}

/* ========================================
 * BaseCard组件标题样式标准化规则
 * ========================================
 *
 * 规则：所有使用BaseCard组件的页面，标题必须使用非粗体样式
 *
 * 强制要求：
 * 1. 字体粗细：所有卡片标题必须使用 font-weight: normal（非粗体）
 * 2. 字体大小：统一使用 16px
 * 3. 对齐方式：标题左对齐，状态右对齐
 * 4. 适用范围：所有使用BaseCard组件的页面
 */

.card-header {
  .card-title,
  .datasource-title,
  .interfacegroup-title,
  .interfacetag-title,
  [class*="-title"] {
    margin: 0;
    margin-top: -2px; // 上移2px，与数据源保持一致
    font-size: 16px; // 统一字体大小
    font-weight: normal !important; // 强制非粗体，与数据源保持一致
    color: #303133; // 统一颜色
    text-align: left; // 标题左对齐
  }

  .status-tag {
    margin-left: auto; // 状态标签右对齐
  }
}

/* ======================================== */
